import { Document, Schema as MongooseSchema } from 'mongoose';
export type TaskPriority = 'low' | 'medium' | 'high';
export type ScrumTaskStatus = 'backlog' | 'todo' | 'doing' | 'done';
export declare class Task extends Document {
    title: string;
    description?: string;
    completed: boolean;
    dueDate?: Date;
    priority: TaskPriority;
    category?: MongooseSchema.Types.ObjectId;
    tags?: string[];
    status?: ScrumTaskStatus;
    user: MongooseSchema.Types.ObjectId;
    project?: MongooseSchema.Types.ObjectId;
}
export declare const TaskSchema: MongooseSchema<Task, import("mongoose").Model<Task, any, any, any, Document<unknown, any, Task, any> & Task & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Task, Document<unknown, {}, import("mongoose").FlatRecord<Task>, {}> & import("mongoose").FlatRecord<Task> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
