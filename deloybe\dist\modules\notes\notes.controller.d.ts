import { NotesService } from './notes.service';
import { CreateNoteDto } from './dto/create-note.dto';
import { UpdateNoteDto } from './dto/update-note.dto';
import { UserDocument } from '../users/schemas/user.schema';
export declare class NotesController {
    private readonly notesService;
    constructor(notesService: NotesService);
    create(createNoteDto: CreateNoteDto, user: UserDocument): Promise<import("./schemas/note.schema").Note>;
    findAll(user: UserDocument): Promise<import("./schemas/note.schema").Note[]>;
    findOne(id: string, user: UserDocument): Promise<import("./schemas/note.schema").Note>;
    update(id: string, updateNoteDto: UpdateNoteDto, user: UserDocument): Promise<import("./schemas/note.schema").Note>;
    remove(id: string, user: UserDocument): {
        success: boolean;
    };
}
