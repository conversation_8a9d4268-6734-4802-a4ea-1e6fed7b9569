import { NotificationsService } from './services/notifications.service';
import { SchedulerService } from './services/scheduler.service';
import { SubscribeEmailDto, UpdateEmailSubscriptionDto } from './dto/subscribe-email.dto';
import { PublicSubscribeEmailDto } from './dto/public-subscribe-email.dto';
export declare class NotificationsController {
    private readonly notificationsService;
    private readonly schedulerService;
    constructor(notificationsService: NotificationsService, schedulerService: SchedulerService);
    subscribeEmail(req: any, subscribeDto: SubscribeEmailDto): Promise<import("./schemas/email-subscription.schema").EmailSubscription>;
    subscribeEmailPublic(subscribeDto: PublicSubscribeEmailDto): Promise<import("./schemas/public-email-subscription.schema").PublicEmailSubscription>;
    getSubscriptions(req: any): Promise<import("./schemas/email-subscription.schema").EmailSubscription[]>;
    updateSubscription(req: any, id: string, updateDto: UpdateEmailSubscriptionDto): Promise<import("./schemas/email-subscription.schema").EmailSubscription>;
    deleteSubscription(req: any, id: string): Promise<{
        message: string;
    }>;
    unsubscribe(token: string): Promise<{
        message: string;
    }>;
    sendReminders(): Promise<{
        sent: number;
        failed: number;
    }>;
    testScheduler(): Promise<any>;
}
