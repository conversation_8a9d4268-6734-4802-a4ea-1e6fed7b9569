import { UserDocument } from '../users/schemas/user.schema';
import { AIService } from './ai.service';
import { ChatRequestDto, ChatResponseDto, TaskSuggestionDto } from './dto/chat.dto';
export declare class AIController {
    private readonly aiService;
    constructor(aiService: AIService);
    chat(chatRequest: ChatRequestDto, user: UserDocument): Promise<ChatResponseDto>;
    suggestPriority(taskSuggestion: TaskSuggestionDto): Promise<{
        priority: 'high' | 'medium' | 'low';
    }>;
    suggestDueDate(taskSuggestion: TaskSuggestionDto): Promise<{
        dueDate: string | null;
    }>;
    getChatHistory(user: UserDocument, sessionId?: string, limit?: number): Promise<import("./schemas/chat-history.schema").ChatHistory[]>;
    clearChatHistory(user: UserDocument, sessionId?: string): Promise<{
        message: string;
    }>;
}
