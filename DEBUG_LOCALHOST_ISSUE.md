# Debug Localhost Issue trong Email

## Vấn đề
Email chào mừng có button "Tạo tài khoản miễn phí" dẫn đến `http://localhost:3000/register` thay vì `https://qltime.vercel.app/register`

## Step-by-step Debug

### Bước 1: Kiểm tra file .env
```bash
node check-env-files.js
```

**Kết quả mong đợi:**
- File `.env` có `FRONTEND_URL=https://qltime.vercel.app`
- Không có file `.env.local` với localhost
- Dockerfile có URL đúng

### Bước 2: Restart Backend HOÀN TOÀN
```bash
# Trong terminal backend:
Ctrl+C                    # Dừng backend
cd deloybe               # Vào thư mục backend
npm run start:dev        # Khởi động lại
```

**<PERSON><PERSON><PERSON> tra logs khi khởi động:**
```
📧 Email Service Configuration:
   EMAIL_USER: <EMAIL>
   FRONTEND_URL: https://qltime.vercel.app
   Final frontend URL: https://qltime.vercel.app
```

### Bước 3: Test với email thật
```bash
node test-with-real-email.js
```

**Theo dõi backend console:**
- Tìm dòng `📧 URLs found in email HTML:`
- Phải thấy: `href="https://qltime.vercel.app/register"`
- KHÔNG được thấy: `href="http://localhost:3000"`

### Bước 4: Kiểm tra email thật
1. Mở Gmail <EMAIL>
2. Tìm email mới nhất với subject "Chào mừng bạn đến với QLTime"
3. Click chuột phải vào button "Tạo tài khoản miễn phí"
4. Chọn "Copy link address"
5. Paste vào notepad để kiểm tra URL

## Phân tích kết quả

### ✅ Trường hợp 1: Backend logs đúng + Email đúng
- Backend logs: `href="https://qltime.vercel.app/register"`
- Email URL: `https://qltime.vercel.app/register`
- **→ ĐÃ SỬA THÀNH CÔNG!**

### ⚠️ Trường hợp 2: Backend logs đúng + Email vẫn localhost
- Backend logs: `href="https://qltime.vercel.app/register"`
- Email URL: `http://localhost:3000/register`
- **→ CACHE EMAIL CLIENT**

**Giải pháp:**
- Thử mở email trên điện thoại
- Thử email client khác (Outlook, Apple Mail)
- Clear cache Gmail
- Đợi vài phút và refresh

### ❌ Trường hợp 3: Backend logs vẫn localhost
- Backend logs: `href="http://localhost:3000/register"`
- **→ BACKEND CHƯA LOAD CONFIG MỚI**

**Giải pháp:**
- Kiểm tra file .env có đúng không
- Restart backend hoàn toàn
- Kiểm tra có file .env.local ghi đè không

## Scripts hỗ trợ

```bash
# Kiểm tra tất cả file .env
node check-env-files.js

# Debug với logs chi tiết
node final-debug-email.js

# Test với email thật
node test-with-real-email.js

# Kiểm tra cấu hình backend
node check-backend-config.js
```

## Checklist Debug

- [ ] File `.env` có `FRONTEND_URL=https://qltime.vercel.app`
- [ ] Không có file `.env.local` với localhost
- [ ] Backend đã restart hoàn toàn
- [ ] Backend logs hiển thị "Final frontend URL: https://qltime.vercel.app"
- [ ] Backend logs hiển thị `href="https://qltime.vercel.app/register"`
- [ ] Email thật có URL đúng

## Lưu ý quan trọng

1. **Phải restart backend** sau khi thay đổi .env
2. **Theo dõi backend logs** để xem URL thực tế
3. **Cache email client** có thể gây hiển thị sai
4. **Test trên nhiều device** để xác nhận

## Kết quả cuối cùng

Sau khi debug, email phải có:
- ✅ "Khám phá QLTime" → `https://qltime.vercel.app`
- ✅ "Tạo tài khoản miễn phí" → `https://qltime.vercel.app/register`
- ✅ "Hủy đăng ký" → `https://qltime.vercel.app/unsubscribe?token=...`
