import { Document } from 'mongoose';
export type PublicEmailSubscriptionDocument = PublicEmailSubscription & Document;
export declare class PublicEmailSubscription {
    email: string;
    isActive: boolean;
    taskReminders: boolean;
    dailySummary: boolean;
    weeklyReport: boolean;
    reminderHours: number;
    unsubscribeToken: string;
    lastNotificationSent: Date;
    name: string;
    emailVerified: boolean;
    verificationToken: string;
}
export declare const PublicEmailSubscriptionSchema: import("mongoose").Schema<PublicEmailSubscription, import("mongoose").Model<PublicEmailSubscription, any, any, any, Document<unknown, any, PublicEmailSubscription, any> & PublicEmailSubscription & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, PublicEmailSubscription, Document<unknown, {}, import("mongoose").FlatRecord<PublicEmailSubscription>, {}> & import("mongoose").FlatRecord<PublicEmailSubscription> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
