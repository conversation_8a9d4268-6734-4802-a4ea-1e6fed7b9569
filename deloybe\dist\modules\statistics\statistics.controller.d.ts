import { StatisticsService } from './statistics.service';
import { StatisticsQueryDto } from './dto/statistics-query.dto';
import { UserDocument } from '../users/schemas/user.schema';
export declare class StatisticsController {
    private readonly statisticsService;
    constructor(statisticsService: StatisticsService);
    getTasksStatistics(user: UserDocument, query: StatisticsQueryDto): Promise<{
        total: number;
        completed: number;
        pending: number;
        overdue: number;
        byPriority: {
            low: number;
            medium: number;
            high: number;
        };
        byStatus: {
            backlog: number;
            todo: number;
            doing: number;
            done: number;
        };
        byCategory: {};
    }>;
    getTimeBlocksStatistics(user: UserDocument, query: StatisticsQueryDto): Promise<{
        totalHours: number;
        completedHours: number;
        completionRate: number;
        byDay: {};
    }>;
    getProductivityStatistics(user: UserDocument, query: StatisticsQueryDto): Promise<{
        productivityScore: number;
        taskCompletionRate: number;
        timeBlockCompletionRate: number;
        dailyScores: {};
    }>;
}
