"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTimeBlockDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class CreateTimeBlockDto {
    title;
    startTime;
    endTime;
    isCompleted;
    taskId;
}
exports.CreateTimeBlockDto = CreateTimeBlockDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề khối thời gian',
        example: 'Họp dự án',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Tiêu đề không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề phải là chuỗi' }),
    __metadata("design:type", String)
], CreateTimeBlockDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian bắt đầu',
        example: '2025-06-15T09:00:00.000Z',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Thời gian bắt đầu không được để trống' }),
    (0, class_validator_1.IsDate)({ message: 'Thời gian bắt đầu phải là ngày hợp lệ' }),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], CreateTimeBlockDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian kết thúc',
        example: '2025-06-15T10:30:00.000Z',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Thời gian kết thúc không được để trống' }),
    (0, class_validator_1.IsDate)({ message: 'Thời gian kết thúc phải là ngày hợp lệ' }),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], CreateTimeBlockDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trạng thái hoàn thành',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'Trạng thái hoàn thành phải là boolean' }),
    __metadata("design:type", Boolean)
], CreateTimeBlockDto.prototype, "isCompleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID công việc liên quan',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateIf)((o) => o.taskId !== null && o.taskId !== undefined && o.taskId !== ''),
    (0, class_validator_1.IsMongoId)({ message: 'ID công việc không hợp lệ' }),
    __metadata("design:type", Object)
], CreateTimeBlockDto.prototype, "taskId", void 0);
//# sourceMappingURL=create-time-block.dto.js.map