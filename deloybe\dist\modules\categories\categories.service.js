"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const category_schema_1 = require("./schemas/category.schema");
let CategoriesService = class CategoriesService {
    categoryModel;
    constructor(categoryModel) {
        this.categoryModel = categoryModel;
    }
    async create(createCategoryDto, userId) {
        const newCategory = new this.categoryModel({
            ...createCategoryDto,
            user: userId,
        });
        return newCategory.save();
    }
    async findAll(userId) {
        return this.categoryModel
            .find({ user: userId })
            .sort({ name: 1 })
            .exec();
    }
    async findById(id, userId) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException(`ID danh mục không hợp lệ: ${id}`);
        }
        const category = await this.categoryModel.findById(id).exec();
        if (!category) {
            throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
        }
        if (category.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền truy cập danh mục này');
        }
        return category;
    }
    async update(id, updateCategoryDto, userId) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException(`ID danh mục không hợp lệ: ${id}`);
        }
        const category = await this.categoryModel.findById(id);
        if (!category) {
            throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
        }
        if (category.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền cập nhật danh mục này');
        }
        const updatedCategory = await this.categoryModel
            .findByIdAndUpdate(id, updateCategoryDto, { new: true })
            .exec();
        if (!updatedCategory) {
            throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
        }
        return updatedCategory;
    }
    async remove(id, userId) {
        if (!mongoose_2.Types.ObjectId.isValid(id)) {
            throw new common_1.BadRequestException(`ID danh mục không hợp lệ: ${id}`);
        }
        const category = await this.categoryModel.findById(id);
        if (!category) {
            throw new common_1.NotFoundException(`Không tìm thấy danh mục với ID: ${id}`);
        }
        if (category.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền xóa danh mục này');
        }
        await this.categoryModel.findByIdAndDelete(id).exec();
    }
};
exports.CategoriesService = CategoriesService;
exports.CategoriesService = CategoriesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(category_schema_1.Category.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], CategoriesService);
//# sourceMappingURL=categories.service.js.map