// Test script để kiểm tra URL trong email đã được sửa
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testEmailUrlFix() {
  console.log('🧪 Testing Email URL Fix...\n');

  try {
    console.log('1️⃣ Test đăng ký email public với URL mới...');
    
    const response = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>', // Email thật để kiểm tra
      name: 'Test URL Fix',
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Email subscription successful:', response.data);
    console.log('\n📧 Kiểm <NAME_EMAIL>');
    console.log('🔗 Các link trong email bây giờ phải là:');
    console.log('   - <PERSON><PERSON><PERSON><PERSON> phá QLTime: https://qltime.vercel.app');
    console.log('   - Tạo tài khoản miễn phí: https://qltime.vercel.app/register');
    console.log('   - Hủy đăng ký: https://qltime.vercel.app/unsubscribe?token=...');
    
    console.log('\n⚠️ QUAN TRỌNG: Restart backend server để áp dụng thay đổi .env!');
    console.log('   Backend cần restart để load FRONTEND_URL mới');

  } catch (error) {
    console.error('❌ Error testing email URL fix:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Chạy test
testEmailUrlFix();
