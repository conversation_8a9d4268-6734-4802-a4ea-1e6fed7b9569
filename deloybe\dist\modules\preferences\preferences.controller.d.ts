import { PreferencesService } from './preferences.service';
import { UpdatePreferenceDto } from './dto/update-preference.dto';
import { UserDocument } from '../users/schemas/user.schema';
export declare class PreferencesController {
    private readonly preferencesService;
    constructor(preferencesService: PreferencesService);
    getPreferences(user: UserDocument): Promise<import("./schemas/preference.schema").PreferenceDocument>;
    updatePreferences(user: UserDocument, updatePreferenceDto: UpdatePreferenceDto): Promise<import("./schemas/preference.schema").PreferenceDocument>;
    patchPreferences(user: UserDocument, updatePreferenceDto: UpdatePreferenceDto): Promise<import("./schemas/preference.schema").PreferenceDocument>;
}
