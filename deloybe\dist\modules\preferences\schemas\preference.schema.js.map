{"version": 3, "file": "preference.schema.js", "sourceRoot": "", "sources": ["../../../../src/modules/preferences/schemas/preference.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAAqE;AACrE,6CAA8C;AASvC,IAAM,UAAU,GAAhB,MAAM,UAAU;IAMrB,IAAI,CAAgC;IAQpC,KAAK,CAAY;IAOjB,QAAQ,CAAS;IAOjB,aAAa,CAAU;IAQvB,YAAY,CAAmB;IAQ/B,WAAW,CAAkB;IAO7B,kBAAkB,CAAU;CAC7B,CAAA;AApDY,gCAAU;AAMrB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE,0BAA0B;KACpC,CAAC;IACD,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,iBAAc,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;8BACnF,iBAAc,CAAC,KAAK,CAAC,QAAQ;wCAAC;AAQpC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,QAAQ;QACrB,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC;QACjC,OAAO,EAAE,QAAQ;KAClB,CAAC;IACD,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;;yCAC9C;AAOjB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,UAAU;QACvB,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;4CACP;AAOjB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;iDACD;AAQvB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,0BAA0B;QACvC,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;QAC9B,OAAO,EAAE,MAAM;KAChB,CAAC;IACD,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;;gDAC3B;AAQ/B;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACf,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;+CACT;AAO7B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,kCAAkC;QAC/C,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;sDACI;qBAnDjB,UAAU;IADtB,IAAA,iBAAM,EAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;GAChB,UAAU,CAoDtB;AAEY,QAAA,gBAAgB,GAAG,wBAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC"}