"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const notifications_service_1 = require("./services/notifications.service");
const scheduler_service_1 = require("./services/scheduler.service");
const subscribe_email_dto_1 = require("./dto/subscribe-email.dto");
const public_subscribe_email_dto_1 = require("./dto/public-subscribe-email.dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
let NotificationsController = class NotificationsController {
    notificationsService;
    schedulerService;
    constructor(notificationsService, schedulerService) {
        this.notificationsService = notificationsService;
        this.schedulerService = schedulerService;
    }
    async subscribeEmail(req, subscribeDto) {
        return this.notificationsService.subscribeEmail(req.user.userId, subscribeDto);
    }
    async subscribeEmailPublic(subscribeDto) {
        return this.notificationsService.subscribeEmailPublic(subscribeDto);
    }
    async getSubscriptions(req) {
        return this.notificationsService.getSubscription(req.user.userId);
    }
    async updateSubscription(req, id, updateDto) {
        return this.notificationsService.updateSubscription(req.user.userId, id, updateDto);
    }
    async deleteSubscription(req, id) {
        return this.notificationsService.deleteSubscription(req.user.userId, id);
    }
    async unsubscribe(token) {
        return this.notificationsService.unsubscribe(token);
    }
    async sendReminders() {
        return this.notificationsService.sendTaskReminders();
    }
    async testScheduler() {
        return this.schedulerService.testSendReminders();
    }
};
exports.NotificationsController = NotificationsController;
__decorate([
    (0, common_1.Post)('email/subscribe'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Đăng ký nhận thông báo email (yêu cầu đăng nhập)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Đăng ký thành công' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dữ liệu không hợp lệ' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, subscribe_email_dto_1.SubscribeEmailDto]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "subscribeEmail", null);
__decorate([
    (0, common_1.Post)('email/subscribe-public'),
    (0, swagger_1.ApiOperation)({ summary: 'Đăng ký nhận thông báo email (không cần đăng nhập)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Đăng ký thành công' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dữ liệu không hợp lệ' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [public_subscribe_email_dto_1.PublicSubscribeEmailDto]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "subscribeEmailPublic", null);
__decorate([
    (0, common_1.Get)('email/subscriptions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách đăng ký email' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lấy danh sách thành công' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "getSubscriptions", null);
__decorate([
    (0, common_1.Put)('email/subscriptions/:id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Cập nhật đăng ký email' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Cập nhật thành công' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy đăng ký' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, subscribe_email_dto_1.UpdateEmailSubscriptionDto]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "updateSubscription", null);
__decorate([
    (0, common_1.Delete)('email/subscriptions/:id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa đăng ký email' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Xóa thành công' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Không tìm thấy đăng ký' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "deleteSubscription", null);
__decorate([
    (0, common_1.Post)('email/unsubscribe'),
    (0, swagger_1.ApiOperation)({ summary: 'Hủy đăng ký email bằng token' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Hủy đăng ký thành công' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Token không hợp lệ' }),
    __param(0, (0, common_1.Query)('token')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "unsubscribe", null);
__decorate([
    (0, common_1.Post)('email/send-reminders'),
    (0, swagger_1.ApiOperation)({ summary: 'Gửi thông báo nhắc nhở (dành cho cron job)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Gửi thông báo thành công' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "sendReminders", null);
__decorate([
    (0, common_1.Post)('test-scheduler'),
    (0, swagger_1.ApiOperation)({ summary: 'Test gửi thông báo thủ công' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Test thành công' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "testScheduler", null);
exports.NotificationsController = NotificationsController = __decorate([
    (0, swagger_1.ApiTags)('notifications'),
    (0, common_1.Controller)('api/notifications'),
    __metadata("design:paramtypes", [notifications_service_1.NotificationsService,
        scheduler_service_1.SchedulerService])
], NotificationsController);
//# sourceMappingURL=notifications.controller.js.map