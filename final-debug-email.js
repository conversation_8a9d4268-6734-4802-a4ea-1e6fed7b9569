// Script debug cuối cùng để tìm nguyên nhân localhost trong email
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function finalDebugEmail() {
  console.log('🔍 FINAL DEBUG - Tìm nguyên nhân localhost trong email...\n');

  try {
    console.log('1️⃣ Test gửi email và theo dõi backend logs...');
    
    const testEmail = `final-debug-${Date.now()}@example.com`;
    
    console.log(`📧 Gửi email test đến: ${testEmail}`);
    console.log('👀 HÃY MỞ CONSOLE BACKEND VÀ THEO DÕI LOGS!');
    console.log('');

    const response = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: testEmail,
      name: 'Final Debug Test',
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Response:', response.data);
    console.log('');

    console.log('2️⃣ Kiểm tra backend logs ngay bây giờ:');
    console.log('');
    console.log('🔍 Tìm các dòng log sau trong console backend:');
    console.log('');
    console.log('   📧 Email Service Configuration:');
    console.log('      EMAIL_USER: <EMAIL>');
    console.log('      FRONTEND_URL: https://qltime.vercel.app');
    console.log('      Final frontend URL: https://qltime.vercel.app');
    console.log('');
    console.log('   📧 Attempting to send email to: ' + testEmail);
    console.log('   📧 Subject: Chào mừng bạn đến với QLTime - Đăng ký thành công!');
    console.log('   📧 URLs found in email HTML:');
    console.log('      href="https://qltime.vercel.app"');
    console.log('      href="https://qltime.vercel.app/register"');
    console.log('      href="https://qltime.vercel.app/unsubscribe?token=..."');
    console.log('');

    console.log('3️⃣ Nếu thấy localhost trong logs:');
    console.log('   ❌ Backend chưa load config mới');
    console.log('   🔄 Cần restart backend hoàn toàn');
    console.log('');

    console.log('4️⃣ Nếu logs đúng nhưng email vẫn có localhost:');
    console.log('   ⚠️ Có thể do cache email client');
    console.log('   🔄 Thử với email client khác');
    console.log('   📱 Kiểm tra trên mobile/web khác nhau');
    console.log('');

    console.log('5️⃣ Test với email thật:');
    console.log('   node test-real-email-final.js');
    console.log('');

    console.log('⏰ Thời gian test:', new Date().toLocaleString('vi-VN'));

  } catch (error) {
    console.error('❌ Error in final debug:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

finalDebugEmail();
