"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NoteSchema = exports.Note = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const swagger_1 = require("@nestjs/swagger");
let Note = class Note extends mongoose_2.Document {
    title;
    content;
    tags;
    color;
    user;
};
exports.Note = Note;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề ghi chú',
        example: 'Ý tưởng dự án mới',
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Note.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung ghi chú',
        example: 'Chi tiết về ý tưởng dự án quản lý thời gian...',
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Note.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Danh sách thẻ',
        example: ['ý tưởng', 'dự án'],
    }),
    (0, mongoose_1.Prop)([String]),
    __metadata("design:type", Array)
], Note.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mã màu',
        example: '#4CAF50',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Note.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người dùng sở hữu',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Note.prototype, "user", void 0);
exports.Note = Note = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Note);
exports.NoteSchema = mongoose_1.SchemaFactory.createForClass(Note);
//# sourceMappingURL=note.schema.js.map