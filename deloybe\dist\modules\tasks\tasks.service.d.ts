import { Model } from 'mongoose';
import { Task } from './schemas/task.schema';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
export declare class TasksService {
    private taskModel;
    constructor(taskModel: Model<Task>);
    create(createTaskDto: CreateTaskDto, userId: string): Promise<Task>;
    findAll(userId: string, filters?: any): Promise<Task[]>;
    findById(id: string, userId: string): Promise<Task>;
    update(id: string, updateTaskDto: UpdateTaskDto, userId: string): Promise<Task>;
    remove(id: string, userId: string): Promise<void>;
    toggleCompletion(id: string, userId: string): Promise<Task>;
    updateStatus(id: string, status: string, userId: string): Promise<Task>;
    findTasksDueSoon(userId: string, hours?: number): Promise<Task[]>;
}
