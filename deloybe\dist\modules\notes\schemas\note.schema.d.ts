import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class Note extends Document {
    title: string;
    content: string;
    tags?: string[];
    color?: string;
    user: MongooseSchema.Types.ObjectId;
}
export declare const NoteSchema: MongooseSchema<Note, import("mongoose").Model<Note, any, any, any, Document<unknown, any, Note, any> & Note & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Note, Document<unknown, {}, import("mongoose").FlatRecord<Note>, {}> & import("mongoose").FlatRecord<Note> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
