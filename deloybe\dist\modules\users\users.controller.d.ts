import { UsersService } from './users.service';
import { UserDocument } from './schemas/user.schema';
import { UpdateUserDto } from './dto/update-user.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    getProfile(user: UserDocument): {
        id: import("mongoose").Types.ObjectId;
        name: string;
        email: string;
        avatar: string | undefined;
    };
    updateProfile(user: UserDocument, updateUserDto: UpdateUserDto): Promise<import("./schemas/user.schema").User>;
}
