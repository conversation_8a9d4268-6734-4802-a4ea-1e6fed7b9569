// Script để kiểm tra cấu hình email server
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function checkEmailConfig() {
  console.log('🔍 Kiểm tra cấu hình email server...\n');

  try {
    // Test 1: Đăng ký email public (không cần auth)
    console.log('1️⃣ Test đăng ký email public...');
    const publicResponse = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>',
      name: 'Test Config User',
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Public subscription successful:', publicResponse.data);

    // Test 2: Test manual scheduler
    console.log('\n2️⃣ Test manual scheduler...');
    const schedulerResponse = await axios.post(`${API_BASE}/notifications/test-scheduler`);
    
    console.log('✅ Manual scheduler response:', schedulerResponse.data);

    // Test 3: Kiểm tra backend logs
    console.log('\n3️⃣ Kiểm tra backend logs...');
    console.log('👀 Hãy kiểm tra console của backend server để xem:');
    console.log('   - Email credentials có được load không');
    console.log('   - Email service connection status');
    console.log('   - Chi tiết quá trình gửi email');

    console.log('\n📋 Thông tin cấu hình cần kiểm tra:');
    console.log('   - EMAIL_USER: Địa chỉ Gmail');
    console.log('   - EMAIL_PASSWORD: App password của Gmail');
    console.log('   - FRONTEND_URL: URL của frontend');

    console.log('\n💡 Nếu email không gửi được:');
    console.log('   1. Kiểm tra Gmail có bật 2FA và tạo App Password');
    console.log('   2. Kiểm tra file .env có đúng thông tin');
    console.log('   3. Restart backend server sau khi thay đổi .env');

  } catch (error) {
    console.error('❌ Error checking email config:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Chạy kiểm tra
checkEmailConfig();
