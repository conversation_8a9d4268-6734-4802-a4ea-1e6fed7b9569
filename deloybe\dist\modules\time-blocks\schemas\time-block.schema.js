"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeBlockSchema = exports.TimeBlock = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const swagger_1 = require("@nestjs/swagger");
let TimeBlock = class TimeBlock extends mongoose_2.Document {
    title;
    startTime;
    endTime;
    isCompleted;
    taskId;
    user;
};
exports.TimeBlock = TimeBlock;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề khối thời gian',
        example: 'Họp dự án',
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], TimeBlock.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian bắt đầu',
        example: '2025-06-15T09:00:00.000Z',
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Date)
], TimeBlock.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Thời gian kết thúc',
        example: '2025-06-15T10:30:00.000Z',
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Date)
], TimeBlock.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái hoàn thành',
        example: false,
    }),
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], TimeBlock.prototype, "isCompleted", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Công việc liên quan',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'Task' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], TimeBlock.prototype, "taskId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người dùng sở hữu',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], TimeBlock.prototype, "user", void 0);
exports.TimeBlock = TimeBlock = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], TimeBlock);
exports.TimeBlockSchema = mongoose_1.SchemaFactory.createForClass(TimeBlock);
//# sourceMappingURL=time-block.schema.js.map