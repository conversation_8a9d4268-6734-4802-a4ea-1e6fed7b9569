// Test email cuối cùng với địa chỉ email chính
const axios = require('axios');

async function testFinalEmail() {
  try {
    console.log('🧪 Testing final email with main email address...');
    const response = await axios.post('http://localhost:3001/api/notifications/email/subscribe-public', {
      email: '<EMAIL>', // Email chính
      name: 'Final Test - Updated Links',
      taskReminders: true,
      dailySummary: true,
      weeklyReport: false,
      reminderHours: 1
    });
    console.log('✅ Email subscription successful:', response.data);
    console.log('📧 Check your main email - all links should go to https://qltime.vercel.app');
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testFinalEmail();
