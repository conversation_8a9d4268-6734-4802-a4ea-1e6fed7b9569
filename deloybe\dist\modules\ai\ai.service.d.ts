import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { ChatHistory, ChatHistoryDocument } from './schemas/chat-history.schema';
import { ChatRequestDto, TaskSuggestionDto } from './dto/chat.dto';
export declare class AIService {
    private chatHistoryModel;
    private configService;
    private readonly logger;
    private ai;
    constructor(chatHistoryModel: Model<ChatHistoryDocument>, configService: ConfigService);
    private initializeAI;
    chat(userId: string, chatRequest: ChatRequestDto): Promise<any>;
    suggestPriority(taskSuggestion: TaskSuggestionDto): Promise<'high' | 'medium' | 'low'>;
    suggestDueDate(taskSuggestion: TaskSuggestionDto): Promise<string | null>;
    getChatHistory(userId: string, sessionId?: string, limit?: number): Promise<ChatHistory[]>;
    clearChatHistory(userId: string, sessionId?: string): Promise<void>;
    private saveChatMessage;
    private fallbackSuggestPriority;
    private fallbackSuggestDueDate;
}
