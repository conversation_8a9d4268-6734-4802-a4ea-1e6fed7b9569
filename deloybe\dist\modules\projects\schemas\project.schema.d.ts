import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class Project extends Document {
    name: string;
    description?: string;
    user: MongooseSchema.Types.ObjectId;
}
export declare const ProjectSchema: MongooseSchema<Project, import("mongoose").Model<Project, any, any, any, Document<unknown, any, Project, any> & Project & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Project, Document<unknown, {}, import("mongoose").FlatRecord<Project>, {}> & import("mongoose").FlatRecord<Project> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
