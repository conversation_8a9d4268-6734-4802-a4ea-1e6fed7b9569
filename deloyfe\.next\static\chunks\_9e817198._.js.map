{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/lib/services/api-service.ts"], "sourcesContent": ["import { Task, Category, TimeBlock, Preference, Note, Project } from '../types';\r\n\r\n// URL cơ sở cho API\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://qltimebe.onrender.com/api';\r\n\r\n// Hàm trợ giúp để xử lý lỗi từ phản hồi fetch\r\nconst handleResponse = async (response: Response) => {\r\n  if (!response.ok) {\r\n    console.log(`handleResponse: Response not OK - Status: ${response.status}, StatusText: ${response.statusText}`);\r\n\r\n    // Xử lý lỗi 401 (Unauthorized) - token hết hạn hoặc không hợp lệ\r\n    if (response.status === 401) {\r\n      console.log('handleResponse: 401 Unauthorized - Xóa token');\r\n      // Xóa token không hợp lệ\r\n      if (typeof window !== 'undefined') {\r\n        localStorage.removeItem('authToken');\r\n      }\r\n    }\r\n\r\n    const errorData = await response.json().catch(() => ({}));\r\n    console.log('handleResponse: Error data:', errorData);\r\n    throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);\r\n  }\r\n\r\n  // Kiểm tra nếu response có content\r\n  const contentType = response.headers.get('content-type');\r\n  if (contentType && contentType.includes('application/json')) {\r\n    const text = await response.text();\r\n    if (text && text !== 'undefined' && text !== 'null' && text.trim() !== '') {\r\n      try {\r\n        return JSON.parse(text);\r\n      } catch (parseError) {\r\n        console.error('JSON parse error:', parseError, 'Text:', text);\r\n        throw new Error('Phản hồi từ server không hợp lệ');\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Nếu không có content-type JSON, trả về null cho DELETE requests\r\n  return null;\r\n};\r\n\r\n// Hàm wrapper để xử lý lỗi kết nối\r\nconst handleFetchError = async (fetchPromise: Promise<Response>) => {\r\n  try {\r\n    return await fetchPromise;\r\n  } catch (error) {\r\n    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\r\n      throw new Error('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng hoặc đảm bảo server đang chạy.');\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Hàm xử lý response đặc biệt cho getById - không throw error cho 404\r\nconst handleResponseForGetById = async (response: Response) => {\r\n  if (!response.ok) {\r\n    if (response.status === 404) {\r\n      // Trả về null thay vì throw error cho 404\r\n      return null;\r\n    }\r\n    const errorData = await response.json().catch(() => ({}));\r\n    throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);\r\n  }\r\n\r\n  // Kiểm tra nếu response có content\r\n  const contentType = response.headers.get('content-type');\r\n  if (contentType && contentType.includes('application/json')) {\r\n    const text = await response.text();\r\n    if (text && text !== 'undefined' && text !== 'null' && text.trim() !== '') {\r\n      try {\r\n        return JSON.parse(text);\r\n      } catch (parseError) {\r\n        console.error('JSON parse error:', parseError, 'Text:', text);\r\n        throw new Error('Phản hồi từ server không hợp lệ');\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Nếu không có content-type JSON, trả về null cho DELETE requests\r\n  return null;\r\n};\r\n\r\n// Lấy token xác thực từ localStorage\r\nconst getAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('authToken');\r\n  }\r\n  return null;\r\n};\r\n\r\n// Tạo headers cơ bản cho các yêu cầu API\r\nconst getHeaders = () => {\r\n  const headers: HeadersInit = {\r\n    'Content-Type': 'application/json',\r\n  };\r\n\r\n  const token = getAuthToken();\r\n  if (token) {\r\n    headers['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  return headers;\r\n};\r\n\r\n// Tạo headers cho DELETE requests (không có Content-Type)\r\nconst getDeleteHeaders = () => {\r\n  const headers: HeadersInit = {};\r\n\r\n  const token = getAuthToken();\r\n  if (token) {\r\n    headers['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  return headers;\r\n};\r\n\r\nexport const ApiService = {\r\n  // AUTH ENDPOINTS\r\n  auth: {\r\n    login: async (email: string, password: string) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/auth/login`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify({ email, password }),\r\n      }));\r\n      const data = await handleResponse(response);\r\n      if (data.token) {\r\n        localStorage.setItem('authToken', data.token);\r\n      }\r\n      return data;\r\n    },\r\n\r\n    register: async (name: string, email: string, password: string) => {\r\n      const response = await fetch(`${API_BASE_URL}/auth/register`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify({ name, email, password }),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    logout: () => {\r\n      localStorage.removeItem('authToken');\r\n    },\r\n\r\n    getCurrentUser: async () => {\r\n      console.log('ApiService.getCurrentUser: Đang gọi API /auth/me...');\r\n      const headers = getHeaders();\r\n      console.log('ApiService.getCurrentUser: Headers:', headers);\r\n\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/auth/me`, {\r\n        headers: headers,\r\n      }));\r\n\r\n      console.log('ApiService.getCurrentUser: Response status:', response.status);\r\n      const result = await handleResponse(response);\r\n      console.log('ApiService.getCurrentUser: Result:', result);\r\n      return result;\r\n    },\r\n  },\r\n\r\n  // TASK ENDPOINTS\r\n  tasks: {\r\n    getAll: async (): Promise<Task[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return data.map((task: any) => ({\r\n        ...task,\r\n        id: task._id || task.id,\r\n      }));\r\n    },\r\n\r\n    getById: async (id: string): Promise<Task | null> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponseForGetById(response);\r\n    },\r\n\r\n    create: async (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(task),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    update: async (id: string, updates: Partial<Omit<Task, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Task> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    toggleCompletion: async (id: string): Promise<Task> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks/${id}/complete`, {\r\n        method: 'PATCH',\r\n        headers: getDeleteHeaders(), // Không cần Content-Type vì không có body\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // CATEGORY ENDPOINTS\r\n  categories: {\r\n    getAll: async (): Promise<Category[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getById: async (id: string): Promise<Category> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    create: async (category: Omit<Category, 'id'>): Promise<Category> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(category),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    update: async (id: string, updates: Partial<Omit<Category, 'id'>>): Promise<Category> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // TIME BLOCK ENDPOINTS\r\n  timeBlocks: {\r\n    getAll: async (): Promise<TimeBlock[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return data.map((timeBlock: any) => ({\r\n        ...timeBlock,\r\n        id: timeBlock._id || timeBlock.id,\r\n      }));\r\n    },\r\n\r\n    getByDate: async (date: string): Promise<TimeBlock[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/date/${date}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return data.map((timeBlock: any) => ({\r\n        ...timeBlock,\r\n        id: timeBlock._id || timeBlock.id,\r\n      }));\r\n    },\r\n\r\n    getById: async (id: string): Promise<TimeBlock> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    create: async (timeBlock: {\r\n      title: string;\r\n      startTime: string;\r\n      endTime: string;\r\n      isCompleted?: boolean;\r\n      taskId?: string | null;\r\n    }): Promise<TimeBlock> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(timeBlock),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    update: async (id: string, updates: Partial<Omit<TimeBlock, 'id'>>): Promise<TimeBlock> => {\r\n      console.log('ApiService: Updating timeBlock with id:', id, 'updates:', updates);\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    toggleCompletion: async (id: string, isCompleted: boolean): Promise<TimeBlock> => {\r\n      console.log('ApiService: Toggling timeBlock completion:', id, isCompleted);\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify({ isCompleted }),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n  },\r\n\r\n  // PREFERENCE ENDPOINTS\r\n  preferences: {\r\n    get: async (): Promise<Preference> => {\r\n      const response = await fetch(`${API_BASE_URL}/preferences`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    update: async (updates: Partial<Omit<Preference, 'id' | 'userId'>>): Promise<Preference> => {\r\n      const response = await fetch(`${API_BASE_URL}/preferences`, {\r\n        method: 'PATCH',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // PROJECT ENDPOINTS\r\n  projects: {\r\n    getAll: async (): Promise<Project[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return data.map((project: any) => ({\r\n        ...project,\r\n        id: project._id || project.id,\r\n      }));\r\n    },\r\n\r\n    getById: async (id: string): Promise<Project> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    create: async (project: {\r\n      name: string;\r\n      description?: string;\r\n    }): Promise<Project> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(project),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    update: async (id: string, updates: any): Promise<Project> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // NOTE ENDPOINTS\r\n  notes: {\r\n    getAll: async (): Promise<Note[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getById: async (id: string): Promise<Note> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    create: async (note: Omit<Note, 'id' | 'createdAt' | 'updatedAt'>): Promise<Note> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(note),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    update: async (id: string, updates: Partial<Omit<Note, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Note> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes/${id}`, {\r\n        method: 'PATCH',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // STATISTICS ENDPOINTS\r\n  statistics: {\r\n    getTaskStats: async (startDate?: string, endDate?: string) => {\r\n      const params = new URLSearchParams();\r\n      if (startDate) params.append('startDate', startDate);\r\n      if (endDate) params.append('endDate', endDate);\r\n\r\n      const url = `${API_BASE_URL}/statistics/tasks${params.toString() ? `?${params.toString()}` : ''}`;\r\n      const response = await fetch(url, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getTimeBlockStats: async (startDate?: string, endDate?: string) => {\r\n      const params = new URLSearchParams();\r\n      if (startDate) params.append('startDate', startDate);\r\n      if (endDate) params.append('endDate', endDate);\r\n\r\n      const url = `${API_BASE_URL}/statistics/time-blocks${params.toString() ? `?${params.toString()}` : ''}`;\r\n      const response = await fetch(url, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getProductivityStats: async (startDate?: string, endDate?: string) => {\r\n      const params = new URLSearchParams();\r\n      if (startDate) params.append('startDate', startDate);\r\n      if (endDate) params.append('endDate', endDate);\r\n\r\n      const url = `${API_BASE_URL}/statistics/productivity${params.toString() ? `?${params.toString()}` : ''}`;\r\n      const response = await fetch(url, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // USER ENDPOINTS\r\n  users: {\r\n    getProfile: async () => {\r\n      const response = await fetch(`${API_BASE_URL}/users/profile`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    updateProfile: async (updates: { name?: string; email?: string; avatar?: string }) => {\r\n      const response = await fetch(`${API_BASE_URL}/users/profile`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // NOTIFICATIONS ENDPOINTS\r\n  notifications: {\r\n    subscribeEmail: async (subscriptionData: {\r\n      email: string;\r\n      taskReminders?: boolean;\r\n      dailySummary?: boolean;\r\n      weeklyReport?: boolean;\r\n      reminderHours?: number;\r\n    }) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscribe`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(subscriptionData),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    subscribeEmailPublic: async (subscriptionData: {\r\n      email: string;\r\n      name?: string;\r\n      taskReminders?: boolean;\r\n      dailySummary?: boolean;\r\n      weeklyReport?: boolean;\r\n      reminderHours?: number;\r\n    }) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscribe-public`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(subscriptionData),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getSubscriptions: async () => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions`, {\r\n        headers: getHeaders(),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    updateSubscription: async (id: string, updateData: any) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updateData),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    deleteSubscription: async (id: string) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getHeaders(),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    unsubscribe: async (token: string) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/unsubscribe?token=${token}`, {\r\n        method: 'POST',\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n\r\n};\r\n"], "names": [], "mappings": ";;;AAGqB;AADrB,oBAAoB;AACpB,MAAM,eAAe,iEAAmC;AAExD,8CAA8C;AAC9C,MAAM,iBAAiB,OAAO;IAC5B,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,SAAS,MAAM,CAAC,cAAc,EAAE,SAAS,UAAU,EAAE;QAE9G,iEAAiE;QACjE,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,QAAQ,GAAG,CAAC;YACZ,yBAAyB;YACzB,wCAAmC;gBACjC,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;IACvF;IAEA,mCAAmC;IACnC,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;IACzC,IAAI,eAAe,YAAY,QAAQ,CAAC,qBAAqB;QAC3D,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,QAAQ,SAAS,eAAe,SAAS,UAAU,KAAK,IAAI,OAAO,IAAI;YACzE,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC;YACpB,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qBAAqB,YAAY,SAAS;gBACxD,MAAM,IAAI,MAAM;YAClB;QACF;QACA,OAAO;IACT;IAEA,kEAAkE;IAClE,OAAO;AACT;AAEA,mCAAmC;AACnC,MAAM,mBAAmB,OAAO;IAC9B,IAAI;QACF,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,oBAAoB;YAC3E,MAAM,IAAI,MAAM;QAClB;QACA,MAAM;IACR;AACF;AAEA,sEAAsE;AACtE,MAAM,2BAA2B,OAAO;IACtC,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,0CAA0C;YAC1C,OAAO;QACT;QACA,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;IACvF;IAEA,mCAAmC;IACnC,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;IACzC,IAAI,eAAe,YAAY,QAAQ,CAAC,qBAAqB;QAC3D,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,QAAQ,SAAS,eAAe,SAAS,UAAU,KAAK,IAAI,OAAO,IAAI;YACzE,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC;YACpB,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qBAAqB,YAAY,SAAS;gBACxD,MAAM,IAAI,MAAM;YAClB;QACF;QACA,OAAO;IACT;IAEA,kEAAkE;IAClE,OAAO;AACT;AAEA,qCAAqC;AACrC,MAAM,eAAe;IACnB,wCAAmC;QACjC,OAAO,aAAa,OAAO,CAAC;IAC9B;;AAEF;AAEA,yCAAyC;AACzC,MAAM,aAAa;IACjB,MAAM,UAAuB;QAC3B,gBAAgB;IAClB;IAEA,MAAM,QAAQ;IACd,IAAI,OAAO;QACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC9C;IAEA,OAAO;AACT;AAEA,0DAA0D;AAC1D,MAAM,mBAAmB;IACvB,MAAM,UAAuB,CAAC;IAE9B,MAAM,QAAQ;IACd,IAAI,OAAO;QACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC9C;IAEA,OAAO;AACT;AAEO,MAAM,aAAa;IACxB,iBAAiB;IACjB,MAAM;QACJ,OAAO,OAAO,OAAe;YAC3B,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;gBAC1E,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,IAAI,KAAK,KAAK,EAAE;gBACd,aAAa,OAAO,CAAC,aAAa,KAAK,KAAK;YAC9C;YACA,OAAO;QACT;QAEA,UAAU,OAAO,MAAc,OAAe;YAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC;QAC1B;QAEA,gBAAgB;YACd,QAAQ,GAAG,CAAC;YACZ,MAAM,UAAU;YAChB,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,QAAQ,CAAC,EAAE;gBACvE,SAAS;YACX;YAEA,QAAQ,GAAG,CAAC,+CAA+C,SAAS,MAAM;YAC1E,MAAM,SAAS,MAAM,eAAe;YACpC,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,OAAO;QACL,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;gBACpD,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO,KAAK,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC9B,GAAG,IAAI;oBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;gBACzB,CAAC;QACH;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,SAAS;YACX;YACA,OAAO,yBAAyB;QAClC;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,kBAAkB,OAAO;YACvB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,GAAG,SAAS,CAAC,EAAE;gBACnE,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,qBAAqB;IACrB,YAAY;QACV,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;gBACzD,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,EAAE,IAAI,EAAE;gBAC/D,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;gBACzD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,EAAE,IAAI,EAAE;gBAC/D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,EAAE,IAAI,EAAE;gBAC/D,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,uBAAuB;IACvB,YAAY;QACV,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO,KAAK,GAAG,CAAC,CAAC,YAAmB,CAAC;oBACnC,GAAG,SAAS;oBACZ,IAAI,UAAU,GAAG,IAAI,UAAU,EAAE;gBACnC,CAAC;QACH;QAEA,WAAW,OAAO;YAChB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,kBAAkB,EAAE,MAAM,EAAE;gBACvE,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO,KAAK,GAAG,CAAC,CAAC,YAAmB,CAAC;oBACnC,GAAG,SAAS;oBACZ,IAAI,UAAU,GAAG,IAAI,UAAU,EAAE;gBACnC,CAAC;QACH;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,IAAI,EAAE;gBAChE,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO;YAOb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO,IAAY;YACzB,QAAQ,GAAG,CAAC,2CAA2C,IAAI,YAAY;YACvE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,IAAI,EAAE;gBAChE,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,IAAI,EAAE;gBAChE,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,kBAAkB,OAAO,IAAY;YACnC,QAAQ,GAAG,CAAC,8CAA8C,IAAI;YAC9D,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,IAAI,EAAE;gBAChE,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAY;YACrC;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;IACF;IAEA,uBAAuB;IACvB,aAAa;QACX,KAAK;YACH,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;IACF;IAEA,oBAAoB;IACpB,UAAU;QACR,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;gBACvD,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO,KAAK,GAAG,CAAC,CAAC,UAAiB,CAAC;oBACjC,GAAG,OAAO;oBACV,IAAI,QAAQ,GAAG,IAAI,QAAQ,EAAE;gBAC/B,CAAC;QACH;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;gBAC7D,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO;YAIb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;gBACvD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;gBAC7D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;gBAC7D,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,iBAAiB;IACjB,OAAO;QACL,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;gBACpD,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,uBAAuB;IACvB,YAAY;QACV,cAAc,OAAO,WAAoB;YACvC,MAAM,SAAS,IAAI;YACnB,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,SAAS,OAAO,MAAM,CAAC,WAAW;YAEtC,MAAM,MAAM,GAAG,aAAa,iBAAiB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YACjG,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,mBAAmB,OAAO,WAAoB;YAC5C,MAAM,SAAS,IAAI;YACnB,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,SAAS,OAAO,MAAM,CAAC,WAAW;YAEtC,MAAM,MAAM,GAAG,aAAa,uBAAuB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YACvG,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,sBAAsB,OAAO,WAAoB;YAC/C,MAAM,SAAS,IAAI;YACnB,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,SAAS,OAAO,MAAM,CAAC,WAAW;YAEtC,MAAM,MAAM,GAAG,aAAa,wBAAwB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YACxG,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,iBAAiB;IACjB,OAAO;QACL,YAAY;YACV,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;gBAC5D,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,eAAe,OAAO;YACpB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;IACF;IAEA,0BAA0B;IAC1B,eAAe;QACb,gBAAgB,OAAO;YAOrB,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,8BAA8B,CAAC,EAAE;gBAC7F,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,sBAAsB,OAAO;YAQ3B,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,qCAAqC,CAAC,EAAE;gBACpG,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,kBAAkB;YAChB,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,kCAAkC,CAAC,EAAE;gBACjG,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,oBAAoB,OAAO,IAAY;YACrC,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,mCAAmC,EAAE,IAAI,EAAE;gBACvG,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,oBAAoB,OAAO;YACzB,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,mCAAmC,EAAE,IAAI,EAAE;gBACvG,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,aAAa,OAAO;YAClB,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,uCAAuC,EAAE,OAAO,EAAE;gBAC9G,QAAQ;YACV;YACA,OAAO,eAAe;QACxB;IACF;AAGF", "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/components/auth/auth-guard.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { ApiService } from '@/lib/services/api-service';\n\ninterface AuthGuardProps {\n  children: React.ReactNode;\n}\n\nexport function AuthGuard({ children }: AuthGuardProps) {\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      console.log('AuthGuard: Bắt đầu kiểm tra authentication...');\n      const token = localStorage.getItem('authToken');\n      console.log('AuthGuard: Token từ localStorage:', token ? `${token.substring(0, 20)}...` : 'null');\n\n      if (!token) {\n        console.log('AuthGuard: Không có token, chuyển hướng đến login');\n        setIsAuthenticated(false);\n        router.push('/login');\n        return;\n      }\n\n      try {\n        console.log('AuthGuard: <PERSON>ang kiểm tra token với API...');\n        // Kiểm tra token có hợp lệ không\n        const user = await ApiService.auth.getCurrentUser();\n        console.log('AuthGuard: Token hợp lệ, user:', user);\n        setIsAuthenticated(true);\n      } catch (error) {\n        console.error('AuthGuard: Token không hợp lệ:', error);\n        localStorage.removeItem('authToken');\n        setIsAuthenticated(false);\n        router.push('/login');\n      }\n    };\n\n    checkAuth();\n  }, [router]);\n\n  // Đang kiểm tra authentication\n  if (isAuthenticated === null) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\n          <p className=\"mt-2 text-muted-foreground\">Đang kiểm tra đăng nhập...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Chưa đăng nhập\n  if (!isAuthenticated) {\n    return null; // Router sẽ chuyển hướng\n  }\n\n  // Đã đăng nhập\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,UAAU,EAAE,QAAQ,EAAkB;;IACpD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;iDAAY;oBAChB,QAAQ,GAAG,CAAC;oBACZ,MAAM,QAAQ,aAAa,OAAO,CAAC;oBACnC,QAAQ,GAAG,CAAC,qCAAqC,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;oBAE1F,IAAI,CAAC,OAAO;wBACV,QAAQ,GAAG,CAAC;wBACZ,mBAAmB;wBACnB,OAAO,IAAI,CAAC;wBACZ;oBACF;oBAEA,IAAI;wBACF,QAAQ,GAAG,CAAC;wBACZ,iCAAiC;wBACjC,MAAM,OAAO,MAAM,2IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,cAAc;wBACjD,QAAQ,GAAG,CAAC,kCAAkC;wBAC9C,mBAAmB;oBACrB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,aAAa,UAAU,CAAC;wBACxB,mBAAmB;wBACnB,OAAO,IAAI,CAAC;oBACd;gBACF;;YAEA;QACF;8BAAG;QAAC;KAAO;IAEX,+BAA+B;IAC/B,IAAI,oBAAoB,MAAM;QAC5B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,iBAAiB;IACjB,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,yBAAyB;IACxC;IAEA,eAAe;IACf,qBAAO;kBAAG;;AACZ;GArDgB;;QAEC,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/components/auth/auth-wrapper.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport { AuthGuard } from './auth-guard';\n\ninterface AuthWrapperProps {\n  children: React.ReactNode;\n}\n\n// Các trang không cần authentication - c<PERSON> thể truy cập tự do\nconst publicRoutes = [\n  '/login',\n  '/register',\n  '/', // Trang chủ\n  '/tasks', // Xem danh sách tasks (chỉ đọc)\n  '/projects', // Xem danh sách projects (chỉ đọc)\n  '/calendar', // Xem lịch (chỉ đọc)\n  '/statistics', // Xem thống kê (chỉ đọc)\n  '/categories', // Xem danh mục (chỉ đọc)\n  '/notes', // Xem ghi chú (chỉ đọc)\n];\n\n// Các trang cần authentication bắt buộc\nconst protectedRoutes = [\n  '/profile',\n  '/settings',\n];\n\nexport function AuthWrapper({ children }: AuthWrapperProps) {\n  const pathname = usePathname();\n\n  // Nếu là trang public, không cần AuthGuard\n  if (publicRoutes.includes(pathname)) {\n    return <>{children}</>;\n  }\n\n  // Nếu là trang protected, bắt buộc phải có AuthGuard\n  if (protectedRoutes.some(route => pathname.startsWith(route))) {\n    return (\n      <AuthGuard>\n        {children}\n      </AuthGuard>\n    );\n  }\n\n  // Các trang khác cũng có thể truy cập tự do (fallback)\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASA,6DAA6D;AAC7D,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,wCAAwC;AACxC,MAAM,kBAAkB;IACtB;IACA;CACD;AAEM,SAAS,YAAY,EAAE,QAAQ,EAAoB;;IACxD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,2CAA2C;IAC3C,IAAI,aAAa,QAAQ,CAAC,WAAW;QACnC,qBAAO;sBAAG;;IACZ;IAEA,qDAAqD;IACrD,IAAI,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;QAC7D,qBACE,6LAAC,8IAAA,CAAA,YAAS;sBACP;;;;;;IAGP;IAEA,uDAAuD;IACvD,qBAAO;kBAAG;;AACZ;GAnBgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/lib/contexts/statistics-context.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useCallback } from 'react';\n\ninterface StatisticsContextType {\n  refreshTrigger: number;\n  triggerRefresh: () => void;\n}\n\nconst StatisticsContext = createContext<StatisticsContextType | undefined>(undefined);\n\nexport function StatisticsProvider({ children }: { children: React.ReactNode }) {\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n\n  const triggerRefresh = useCallback(() => {\n    console.log('StatisticsContext: Triggering statistics refresh');\n    setRefreshTrigger(prev => prev + 1);\n  }, []);\n\n  return (\n    <StatisticsContext.Provider value={{ refreshTrigger, triggerRefresh }}>\n      {children}\n    </StatisticsContext.Provider>\n  );\n}\n\nexport function useStatistics() {\n  const context = useContext(StatisticsContext);\n  if (context === undefined) {\n    console.error('useStatistics must be used within a StatisticsProvider');\n    // Return default values instead of throwing error\n    return {\n      refreshTrigger: 0,\n      triggerRefresh: () => console.warn('StatisticsProvider not found')\n    };\n  }\n  return context;\n}\n\n// Hook để trigger refresh từ bất kỳ đâu\nexport function useStatisticsRefresh() {\n  const { triggerRefresh } = useStatistics();\n  return triggerRefresh;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AASA,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAqC;AAEpE,SAAS,mBAAmB,EAAE,QAAQ,EAAiC;;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACjC,QAAQ,GAAG,CAAC;YACZ;kEAAkB,CAAA,OAAQ,OAAO;;QACnC;yDAAG,EAAE;IAEL,qBACE,6LAAC,kBAAkB,QAAQ;QAAC,OAAO;YAAE;YAAgB;QAAe;kBACjE;;;;;;AAGP;GAbgB;KAAA;AAeT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,QAAQ,KAAK,CAAC;QACd,kDAAkD;QAClD,OAAO;YACL,gBAAgB;YAChB,gBAAgB,IAAM,QAAQ,IAAI,CAAC;QACrC;IACF;IACA,OAAO;AACT;IAXgB;AAcT,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,OAAO;AACT;IAHgB;;QACa", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/app/providers.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ThemeProvider } from \"next-themes\";\r\nimport { ReactNode } from \"react\";\r\nimport { AuthWrapper } from \"@/components/auth/auth-wrapper\";\r\nimport { StatisticsProvider } from \"@/lib/contexts/statistics-context\";\r\n\r\nexport function Providers({ children }: { children: ReactNode }) {\r\n  return (\r\n    <ThemeProvider\r\n      attribute=\"class\"\r\n      defaultTheme=\"system\"\r\n      enableSystem\r\n    >\r\n      <AuthWrapper>\r\n        <StatisticsProvider>\r\n          {children}\r\n        </StatisticsProvider>\r\n      </AuthWrapper>\r\n    </ThemeProvider>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAOO,SAAS,UAAU,EAAE,QAAQ,EAA2B;IAC7D,qBACE,6LAAC,mJAAA,CAAA,gBAAa;QACZ,WAAU;QACV,cAAa;QACb,YAAY;kBAEZ,cAAA,6LAAC,gJAAA,CAAA,cAAW;sBACV,cAAA,6LAAC,mJAAA,CAAA,qBAAkB;0BAChB;;;;;;;;;;;;;;;;AAKX;KAdgB", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/lib/utils/json-utils.ts"], "sourcesContent": ["/**\r\n * Utility functions for safe JSON operations\r\n */\r\n\r\n/**\r\n * Safely parse JSON string, returns null if parsing fails or input is invalid\r\n */\r\nexport const safeJsonParse = <T = any>(jsonString: string | null | undefined): T | null => {\r\n  if (!jsonString || jsonString === 'undefined' || jsonString === 'null' || jsonString.trim() === '') {\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    return JSON.parse(jsonString) as T;\r\n  } catch (error) {\r\n    console.warn('Failed to parse JSON:', error, 'Input:', jsonString);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Safely stringify object to JSON, returns empty string if fails\r\n */\r\nexport const safeJsonStringify = (obj: any): string => {\r\n  try {\r\n    return JSON.stringify(obj);\r\n  } catch (error) {\r\n    console.warn('Failed to stringify JSON:', error, 'Object:', obj);\r\n    return '';\r\n  }\r\n};\r\n\r\n/**\r\n * Safely get and parse item from localStorage\r\n */\r\nexport const safeLocalStorageGet = <T = any>(key: string): T | null => {\r\n  if (typeof window === 'undefined') {\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    const item = localStorage.getItem(key);\r\n    return safeJsonParse<T>(item);\r\n  } catch (error) {\r\n    console.warn('Failed to get from localStorage:', error, 'Key:', key);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Safely set item to localStorage\r\n */\r\nexport const safeLocalStorageSet = (key: string, value: any): boolean => {\r\n  if (typeof window === 'undefined') {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    const jsonString = safeJsonStringify(value);\r\n    if (jsonString) {\r\n      localStorage.setItem(key, jsonString);\r\n      return true;\r\n    }\r\n    return false;\r\n  } catch (error) {\r\n    console.warn('Failed to set to localStorage:', error, 'Key:', key, 'Value:', value);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Safely remove item from localStorage\r\n */\r\nexport const safeLocalStorageRemove = (key: string): boolean => {\r\n  if (typeof window === 'undefined') {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    localStorage.removeItem(key);\r\n    return true;\r\n  } catch (error) {\r\n    console.warn('Failed to remove from localStorage:', error, 'Key:', key);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Clear invalid localStorage items that contain 'undefined' or 'null' strings\r\n */\r\nexport const cleanupLocalStorage = (): number => {\r\n  if (typeof window === 'undefined') {\r\n    return 0;\r\n  }\r\n\r\n  let cleanedCount = 0;\r\n  const keysToRemove: string[] = [];\r\n\r\n  try {\r\n    for (let i = 0; i < localStorage.length; i++) {\r\n      const key = localStorage.key(i);\r\n      if (key) {\r\n        const value = localStorage.getItem(key);\r\n        if (value === 'undefined' || value === 'null' || value === '') {\r\n          keysToRemove.push(key);\r\n        }\r\n      }\r\n    }\r\n\r\n    keysToRemove.forEach(key => {\r\n      localStorage.removeItem(key);\r\n      cleanedCount++;\r\n    });\r\n\r\n    if (cleanedCount > 0) {\r\n      console.log(`Cleaned up ${cleanedCount} invalid localStorage items`);\r\n    }\r\n  } catch (error) {\r\n    console.warn('Failed to cleanup localStorage:', error);\r\n  }\r\n\r\n  return cleanedCount;\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;;;;;AACM,MAAM,gBAAgB,CAAU;IACrC,IAAI,CAAC,cAAc,eAAe,eAAe,eAAe,UAAU,WAAW,IAAI,OAAO,IAAI;QAClG,OAAO;IACT;IAEA,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,yBAAyB,OAAO,UAAU;QACvD,OAAO;IACT;AACF;AAKO,MAAM,oBAAoB,CAAC;IAChC,IAAI;QACF,OAAO,KAAK,SAAS,CAAC;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,6BAA6B,OAAO,WAAW;QAC5D,OAAO;IACT;AACF;AAKO,MAAM,sBAAsB,CAAU;IAC3C,uCAAmC;;IAEnC;IAEA,IAAI;QACF,MAAM,OAAO,aAAa,OAAO,CAAC;QAClC,OAAO,cAAiB;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,oCAAoC,OAAO,QAAQ;QAChE,OAAO;IACT;AACF;AAKO,MAAM,sBAAsB,CAAC,KAAa;IAC/C,uCAAmC;;IAEnC;IAEA,IAAI;QACF,MAAM,aAAa,kBAAkB;QACrC,IAAI,YAAY;YACd,aAAa,OAAO,CAAC,KAAK;YAC1B,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,kCAAkC,OAAO,QAAQ,KAAK,UAAU;QAC7E,OAAO;IACT;AACF;AAKO,MAAM,yBAAyB,CAAC;IACrC,uCAAmC;;IAEnC;IAEA,IAAI;QACF,aAAa,UAAU,CAAC;QACxB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,uCAAuC,OAAO,QAAQ;QACnE,OAAO;IACT;AACF;AAKO,MAAM,sBAAsB;IACjC,uCAAmC;;IAEnC;IAEA,IAAI,eAAe;IACnB,MAAM,eAAyB,EAAE;IAEjC,IAAI;QACF,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;YAC7B,IAAI,KAAK;gBACP,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,UAAU,eAAe,UAAU,UAAU,UAAU,IAAI;oBAC7D,aAAa,IAAI,CAAC;gBACpB;YACF;QACF;QAEA,aAAa,OAAO,CAAC,CAAA;YACnB,aAAa,UAAU,CAAC;YACxB;QACF;QAEA,IAAI,eAAe,GAAG;YACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,2BAA2B,CAAC;QACrE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,mCAAmC;IAClD;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/components/app-initializer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { cleanupLocalStorage } from '@/lib/utils/json-utils';\r\n\r\n/**\r\n * Component để khởi tạo app và dọn dẹp dữ liệu không hợp lệ\r\n */\r\nexport function AppInitializer() {\r\n  useEffect(() => {\r\n    // Cleanup localStorage khi app khởi động\r\n    const cleanedCount = cleanupLocalStorage();\r\n\r\n    if (cleanedCount > 0) {\r\n      console.log(`AppInitializer: Đã dọn dẹp ${cleanedCount} mục localStorage không hợp lệ`);\r\n    }\r\n\r\n    // Global error handler cho unhandled promise rejections\r\n    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {\r\n      if (event.reason && event.reason.message) {\r\n        const message = event.reason.message;\r\n        if (message.includes('not valid JSON') || message.includes('\"undefined\" is not valid JSON')) {\r\n          console.warn('Caught JSON parsing error in promise:', event.reason);\r\n          event.preventDefault(); // Prevent the error from being logged to console\r\n          return;\r\n        }\r\n      }\r\n    };\r\n\r\n    // Global error handler cho window errors\r\n    const handleError = (event: ErrorEvent) => {\r\n      if (event.message) {\r\n        if (event.message.includes('not valid JSON') || event.message.includes('\"undefined\" is not valid JSON')) {\r\n          console.warn('Caught JSON parsing error:', event.message);\r\n          event.preventDefault();\r\n          return;\r\n        }\r\n      }\r\n    };\r\n\r\n    // Intercept console.error để filter các lỗi JSON parsing và empty src\r\n    const originalConsoleError = console.error;\r\n    console.error = (...args: any[]) => {\r\n      const message = args.join(' ');\r\n\r\n      // Suppress JSON parsing errors\r\n      if (message.includes('\"undefined\" is not valid JSON') || message.includes('not valid JSON')) {\r\n        console.warn('Suppressed JSON parsing error:', ...args);\r\n        return;\r\n      }\r\n\r\n      // Suppress empty src attribute warnings\r\n      if (message.includes('An empty string (\"\") was passed to the src attribute')) {\r\n        console.debug('Suppressed empty src warning:', ...args);\r\n        return;\r\n      }\r\n\r\n      // Call original console.error for other errors\r\n      originalConsoleError.apply(console, args);\r\n    };\r\n\r\n    // Add event listeners\r\n    window.addEventListener('unhandledrejection', handleUnhandledRejection);\r\n    window.addEventListener('error', handleError);\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection);\r\n      window.removeEventListener('error', handleError);\r\n      // Restore original console.error\r\n      console.error = originalConsoleError;\r\n    };\r\n  }, []);\r\n\r\n  // Component này không render gì\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAQO,SAAS;;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,yCAAyC;YACzC,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD;YAEvC,IAAI,eAAe,GAAG;gBACpB,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa,8BAA8B,CAAC;YACxF;YAEA,wDAAwD;YACxD,MAAM;qEAA2B,CAAC;oBAChC,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;wBACxC,MAAM,UAAU,MAAM,MAAM,CAAC,OAAO;wBACpC,IAAI,QAAQ,QAAQ,CAAC,qBAAqB,QAAQ,QAAQ,CAAC,kCAAkC;4BAC3F,QAAQ,IAAI,CAAC,yCAAyC,MAAM,MAAM;4BAClE,MAAM,cAAc,IAAI,iDAAiD;4BACzE;wBACF;oBACF;gBACF;;YAEA,yCAAyC;YACzC,MAAM;wDAAc,CAAC;oBACnB,IAAI,MAAM,OAAO,EAAE;wBACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB,MAAM,OAAO,CAAC,QAAQ,CAAC,kCAAkC;4BACvG,QAAQ,IAAI,CAAC,8BAA8B,MAAM,OAAO;4BACxD,MAAM,cAAc;4BACpB;wBACF;oBACF;gBACF;;YAEA,sEAAsE;YACtE,MAAM,uBAAuB,QAAQ,KAAK;YAC1C,QAAQ,KAAK;4CAAG,CAAC,GAAG;oBAClB,MAAM,UAAU,KAAK,IAAI,CAAC;oBAE1B,+BAA+B;oBAC/B,IAAI,QAAQ,QAAQ,CAAC,oCAAoC,QAAQ,QAAQ,CAAC,mBAAmB;wBAC3F,QAAQ,IAAI,CAAC,qCAAqC;wBAClD;oBACF;oBAEA,wCAAwC;oBACxC,IAAI,QAAQ,QAAQ,CAAC,yDAAyD;wBAC5E,QAAQ,KAAK,CAAC,oCAAoC;wBAClD;oBACF;oBAEA,+CAA+C;oBAC/C,qBAAqB,KAAK,CAAC,SAAS;gBACtC;;YAEA,sBAAsB;YACtB,OAAO,gBAAgB,CAAC,sBAAsB;YAC9C,OAAO,gBAAgB,CAAC,SAAS;YAEjC,mBAAmB;YACnB;4CAAO;oBACL,OAAO,mBAAmB,CAAC,sBAAsB;oBACjD,OAAO,mBAAmB,CAAC,SAAS;oBACpC,iCAAiC;oBACjC,QAAQ,KAAK,GAAG;gBAClB;;QACF;mCAAG,EAAE;IAEL,gCAAgC;IAChC,OAAO;AACT;GApEgB;KAAA", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          },\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        Error(\"react-stack-top-frame\"),\n        createTask(getTaskName(type))\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT,GACJ;IACF,IAAI,yBAAyB,CAAC;IAC9B,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,MAAM,0BACN,WAAW,YAAY;IAE3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAqC,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,SAAS,eAAe,EAAC,IAAE;QAAC;QAAQ;KAAO;IAAC,SAAS,EAAE,CAAC;QAAE,CAAC,MAAM,OAAO,CAAC,KAAG,IAAE;YAAC;SAAE,EAAE,OAAO,CAAC,CAAA;YAAI,IAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG;YAAE,IAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE,EAAE,YAAY,CAAC,GAAE;QAAE,IAAG,EAAE;IAAE;IAAC,SAAS,EAAE,CAAC;QAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,KAAK,CAAC,WAAW,GAAC,CAAC;IAAC;IAAC,SAAS;QAAI,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAC,SAAO;IAAO;IAAC,IAAG,GAAE,EAAE;SAAQ,IAAG;QAAC,IAAI,IAAE,aAAa,OAAO,CAAC,MAAI,GAAE,IAAE,KAAG,MAAI,WAAS,MAAI;QAAE,EAAE;IAAE,EAAC,OAAM,GAAE,CAAC;AAAC;AAAE,IAAI,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,gCAA+B,IAAE,OAAO,UAAQ,aAAY,IAAE,CAAA,GAAA,6JAAA,CAAA,gBAAe,AAAD,EAAE,KAAK,IAAG,IAAE;IAAC,UAAS,CAAA,KAAI;IAAE,QAAO,EAAE;AAAA,GAAE,IAAE;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,6JAAA,CAAA,aAAY,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAA,GAAA,6JAAA,CAAA,aAAY,AAAD,EAAE,KAAG,CAAA,GAAA,6JAAA,CAAA,gBAAe,AAAD,EAAE,6JAAA,CAAA,WAAU,EAAC,MAAK,EAAE,QAAQ,IAAE,CAAA,GAAA,6JAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,GAAG,CAAC;IAAA,IAAG,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,CAAC,EAAC,aAAY,CAAC,EAAC,2BAA0B,IAAE,CAAC,CAAC,EAAC,cAAa,IAAE,CAAC,CAAC,EAAC,mBAAkB,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,OAAO,EAAC,QAAO,IAAE,CAAC,EAAC,cAAa,IAAE,IAAE,WAAS,OAAO,EAAC,WAAU,IAAE,YAAY,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,6JAAA,CAAA,WAAU,AAAD;sBAAE,IAAI,EAAE,GAAE;sBAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,6JAAA,CAAA,WAAU,AAAD;sBAAE,IAAI,MAAI,WAAS,MAAI;sBAAG,IAAE,IAAE,OAAO,MAAM,CAAC,KAAG,GAAE,IAAE,CAAA,GAAA,6JAAA,CAAA,cAAa,AAAD;4BAAE,CAAA;YAAI,IAAI,IAAE;YAAE,IAAG,CAAC,GAAE;YAAO,MAAI,YAAU,KAAG,CAAC,IAAE,GAAG;YAAE,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,EAAE,KAAG,MAAK,IAAE,SAAS,eAAe,EAAC;sCAAE,CAAA;oBAAI,MAAI,UAAQ,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,KAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,IAAE,EAAE,UAAU,CAAC,YAAU,CAAC,IAAE,EAAE,YAAY,CAAC,GAAE,KAAG,EAAE,eAAe,CAAC,EAAE;gBAAC;;YAAE,IAAG,MAAM,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,KAAG,EAAE,IAAG,GAAE;gBAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE,MAAK,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE;gBAAE,EAAE,KAAK,CAAC,WAAW,GAAC;YAAC;YAAC,KAAG,QAAM;QAAG;2BAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,6JAAA,CAAA,cAAa,AAAD;4BAAE,CAAA;YAAI,IAAI,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;YAAE,EAAE;YAAG,IAAG;gBAAC,aAAa,OAAO,CAAC,GAAE;YAAE,EAAC,OAAM,GAAE,CAAC;QAAC;2BAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,6JAAA,CAAA,cAAa,AAAD;4BAAE,CAAA;YAAI,IAAI,IAAE,EAAE;YAAG,EAAE,IAAG,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE;QAAS;2BAAE;QAAC;QAAE;KAAE;IAAE,CAAA,GAAA,6JAAA,CAAA,YAAW,AAAD;uBAAE;YAAK,IAAI,IAAE,OAAO,UAAU,CAAC;YAAG,OAAO,EAAE,WAAW,CAAC,IAAG,EAAE;+BAAG,IAAI,EAAE,cAAc,CAAC;;QAAE;sBAAE;QAAC;KAAE,GAAE,CAAA,GAAA,6JAAA,CAAA,YAAW,AAAD;uBAAE;YAAK,IAAI;iCAAE,CAAA;oBAAI,EAAE,GAAG,KAAG,KAAG,CAAC,EAAE,QAAQ,GAAC,EAAE,EAAE,QAAQ,IAAE,EAAE,EAAE;gBAAC;;YAAE,OAAO,OAAO,gBAAgB,CAAC,WAAU;+BAAG,IAAI,OAAO,mBAAmB,CAAC,WAAU;;QAAE;sBAAE;QAAC;KAAE,GAAE,CAAA,GAAA,6JAAA,CAAA,YAAW,AAAD;uBAAE;YAAK,EAAE,KAAG,OAAK,IAAE;QAAE;sBAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,6JAAA,CAAA,UAAS,AAAD;wBAAE,IAAI,CAAC;gBAAC,OAAM;gBAAE,UAAS;gBAAE,aAAY;gBAAE,eAAc,MAAI,WAAS,IAAE;gBAAE,QAAO,IAAE;uBAAI;oBAAE;iBAAS,GAAC;gBAAE,aAAY,IAAE,IAAE,KAAK;YAAC,CAAC;uBAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,OAAO,CAAA,GAAA,6JAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,aAAY;QAAE,YAAW;QAAE,WAAU;QAAE,cAAa;QAAE,mBAAkB;QAAE,cAAa;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;QAAE,aAAY;IAAC,IAAG;AAAE,GAAE,IAAE,CAAA,GAAA,6JAAA,CAAA,OAAM,AAAD,EAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,mBAAkB,CAAC,EAAC,cAAa,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAI,IAAE,KAAK,SAAS,CAAC;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,EAAE,KAAK,CAAC,GAAE,CAAC;IAAG,OAAO,CAAA,GAAA,6JAAA,CAAA,gBAAe,AAAD,EAAE,UAAS;QAAC,GAAG,CAAC;QAAC,0BAAyB,CAAC;QAAE,OAAM,OAAO,UAAQ,cAAY,IAAE;QAAG,yBAAwB;YAAC,QAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAAA;IAAC;AAAE,IAAG,IAAE,CAAC,GAAE;IAAK,IAAG,GAAE;IAAO,IAAI;IAAE,IAAG;QAAC,IAAE,aAAa,OAAO,CAAC,MAAI,KAAK;IAAC,EAAC,OAAM,GAAE,CAAC;IAAC,OAAO,KAAG;AAAC,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,SAAS,aAAa,CAAC;IAAS,OAAO,KAAG,EAAE,YAAY,CAAC,SAAQ,IAAG,EAAE,WAAW,CAAC,SAAS,cAAc,CAAC,iLAAgL,SAAS,IAAI,CAAC,WAAW,CAAC,IAAG;QAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI,GAAE,WAAW;YAAK,SAAS,IAAI,CAAC,WAAW,CAAC;QAAE,GAAE;IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAC,KAAG,CAAC,IAAE,OAAO,UAAU,CAAC,EAAE,GAAE,EAAE,OAAO,GAAC,SAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}