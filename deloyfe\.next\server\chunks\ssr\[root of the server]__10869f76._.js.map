{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/lib/services/api-service.ts"], "sourcesContent": ["import { Task, Category, TimeBlock, Preference, Note, Project } from '../types';\r\n\r\n// URL cơ sở cho API\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://qltimebe.onrender.com/api';\r\n\r\n// Hàm trợ giúp để xử lý lỗi từ phản hồi fetch\r\nconst handleResponse = async (response: Response) => {\r\n  if (!response.ok) {\r\n    console.log(`handleResponse: Response not OK - Status: ${response.status}, StatusText: ${response.statusText}`);\r\n\r\n    // Xử lý lỗi 401 (Unauthorized) - token hết hạn hoặc không hợp lệ\r\n    if (response.status === 401) {\r\n      console.log('handleResponse: 401 Unauthorized - Xóa token');\r\n      // Xóa token không hợp lệ\r\n      if (typeof window !== 'undefined') {\r\n        localStorage.removeItem('authToken');\r\n      }\r\n    }\r\n\r\n    const errorData = await response.json().catch(() => ({}));\r\n    console.log('handleResponse: Error data:', errorData);\r\n    throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);\r\n  }\r\n\r\n  // Kiểm tra nếu response có content\r\n  const contentType = response.headers.get('content-type');\r\n  if (contentType && contentType.includes('application/json')) {\r\n    const text = await response.text();\r\n    if (text && text !== 'undefined' && text !== 'null' && text.trim() !== '') {\r\n      try {\r\n        return JSON.parse(text);\r\n      } catch (parseError) {\r\n        console.error('JSON parse error:', parseError, 'Text:', text);\r\n        throw new Error('Phản hồi từ server không hợp lệ');\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Nếu không có content-type JSON, trả về null cho DELETE requests\r\n  return null;\r\n};\r\n\r\n// Hàm wrapper để xử lý lỗi kết nối\r\nconst handleFetchError = async (fetchPromise: Promise<Response>) => {\r\n  try {\r\n    return await fetchPromise;\r\n  } catch (error) {\r\n    if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\r\n      throw new Error('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng hoặc đảm bảo server đang chạy.');\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Hàm xử lý response đặc biệt cho getById - không throw error cho 404\r\nconst handleResponseForGetById = async (response: Response) => {\r\n  if (!response.ok) {\r\n    if (response.status === 404) {\r\n      // Trả về null thay vì throw error cho 404\r\n      return null;\r\n    }\r\n    const errorData = await response.json().catch(() => ({}));\r\n    throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);\r\n  }\r\n\r\n  // Kiểm tra nếu response có content\r\n  const contentType = response.headers.get('content-type');\r\n  if (contentType && contentType.includes('application/json')) {\r\n    const text = await response.text();\r\n    if (text && text !== 'undefined' && text !== 'null' && text.trim() !== '') {\r\n      try {\r\n        return JSON.parse(text);\r\n      } catch (parseError) {\r\n        console.error('JSON parse error:', parseError, 'Text:', text);\r\n        throw new Error('Phản hồi từ server không hợp lệ');\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // Nếu không có content-type JSON, trả về null cho DELETE requests\r\n  return null;\r\n};\r\n\r\n// Lấy token xác thực từ localStorage\r\nconst getAuthToken = () => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('authToken');\r\n  }\r\n  return null;\r\n};\r\n\r\n// Tạo headers cơ bản cho các yêu cầu API\r\nconst getHeaders = () => {\r\n  const headers: HeadersInit = {\r\n    'Content-Type': 'application/json',\r\n  };\r\n\r\n  const token = getAuthToken();\r\n  if (token) {\r\n    headers['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  return headers;\r\n};\r\n\r\n// Tạo headers cho DELETE requests (không có Content-Type)\r\nconst getDeleteHeaders = () => {\r\n  const headers: HeadersInit = {};\r\n\r\n  const token = getAuthToken();\r\n  if (token) {\r\n    headers['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  return headers;\r\n};\r\n\r\nexport const ApiService = {\r\n  // AUTH ENDPOINTS\r\n  auth: {\r\n    login: async (email: string, password: string) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/auth/login`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify({ email, password }),\r\n      }));\r\n      const data = await handleResponse(response);\r\n      if (data.token) {\r\n        localStorage.setItem('authToken', data.token);\r\n      }\r\n      return data;\r\n    },\r\n\r\n    register: async (name: string, email: string, password: string) => {\r\n      const response = await fetch(`${API_BASE_URL}/auth/register`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify({ name, email, password }),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    logout: () => {\r\n      localStorage.removeItem('authToken');\r\n    },\r\n\r\n    getCurrentUser: async () => {\r\n      console.log('ApiService.getCurrentUser: Đang gọi API /auth/me...');\r\n      const headers = getHeaders();\r\n      console.log('ApiService.getCurrentUser: Headers:', headers);\r\n\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/auth/me`, {\r\n        headers: headers,\r\n      }));\r\n\r\n      console.log('ApiService.getCurrentUser: Response status:', response.status);\r\n      const result = await handleResponse(response);\r\n      console.log('ApiService.getCurrentUser: Result:', result);\r\n      return result;\r\n    },\r\n  },\r\n\r\n  // TASK ENDPOINTS\r\n  tasks: {\r\n    getAll: async (): Promise<Task[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return data.map((task: any) => ({\r\n        ...task,\r\n        id: task._id || task.id,\r\n      }));\r\n    },\r\n\r\n    getById: async (id: string): Promise<Task | null> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponseForGetById(response);\r\n    },\r\n\r\n    create: async (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(task),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    update: async (id: string, updates: Partial<Omit<Task, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Task> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    toggleCompletion: async (id: string): Promise<Task> => {\r\n      const response = await fetch(`${API_BASE_URL}/tasks/${id}/complete`, {\r\n        method: 'PATCH',\r\n        headers: getDeleteHeaders(), // Không cần Content-Type vì không có body\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // CATEGORY ENDPOINTS\r\n  categories: {\r\n    getAll: async (): Promise<Category[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getById: async (id: string): Promise<Category> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    create: async (category: Omit<Category, 'id'>): Promise<Category> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(category),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    update: async (id: string, updates: Partial<Omit<Category, 'id'>>): Promise<Category> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/categories/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // TIME BLOCK ENDPOINTS\r\n  timeBlocks: {\r\n    getAll: async (): Promise<TimeBlock[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return data.map((timeBlock: any) => ({\r\n        ...timeBlock,\r\n        id: timeBlock._id || timeBlock.id,\r\n      }));\r\n    },\r\n\r\n    getByDate: async (date: string): Promise<TimeBlock[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/date/${date}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return data.map((timeBlock: any) => ({\r\n        ...timeBlock,\r\n        id: timeBlock._id || timeBlock.id,\r\n      }));\r\n    },\r\n\r\n    getById: async (id: string): Promise<TimeBlock> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    create: async (timeBlock: {\r\n      title: string;\r\n      startTime: string;\r\n      endTime: string;\r\n      isCompleted?: boolean;\r\n      taskId?: string | null;\r\n    }): Promise<TimeBlock> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(timeBlock),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    update: async (id: string, updates: Partial<Omit<TimeBlock, 'id'>>): Promise<TimeBlock> => {\r\n      console.log('ApiService: Updating timeBlock with id:', id, 'updates:', updates);\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    toggleCompletion: async (id: string, isCompleted: boolean): Promise<TimeBlock> => {\r\n      console.log('ApiService: Toggling timeBlock completion:', id, isCompleted);\r\n      const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify({ isCompleted }),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n  },\r\n\r\n  // PREFERENCE ENDPOINTS\r\n  preferences: {\r\n    get: async (): Promise<Preference> => {\r\n      const response = await fetch(`${API_BASE_URL}/preferences`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    update: async (updates: Partial<Omit<Preference, 'id' | 'userId'>>): Promise<Preference> => {\r\n      const response = await fetch(`${API_BASE_URL}/preferences`, {\r\n        method: 'PATCH',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // PROJECT ENDPOINTS\r\n  projects: {\r\n    getAll: async (): Promise<Project[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return data.map((project: any) => ({\r\n        ...project,\r\n        id: project._id || project.id,\r\n      }));\r\n    },\r\n\r\n    getById: async (id: string): Promise<Project> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    create: async (project: {\r\n      name: string;\r\n      description?: string;\r\n    }): Promise<Project> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(project),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    update: async (id: string, updates: any): Promise<Project> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      const data = await handleResponse(response);\r\n      // Map _id to id for frontend compatibility\r\n      return {\r\n        ...data,\r\n        id: data._id || data.id,\r\n      };\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // NOTE ENDPOINTS\r\n  notes: {\r\n    getAll: async (): Promise<Note[]> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getById: async (id: string): Promise<Note> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes/${id}`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    create: async (note: Omit<Note, 'id' | 'createdAt' | 'updatedAt'>): Promise<Note> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(note),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    update: async (id: string, updates: Partial<Omit<Note, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Note> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes/${id}`, {\r\n        method: 'PATCH',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    delete: async (id: string): Promise<void> => {\r\n      const response = await fetch(`${API_BASE_URL}/notes/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getDeleteHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // STATISTICS ENDPOINTS\r\n  statistics: {\r\n    getTaskStats: async (startDate?: string, endDate?: string) => {\r\n      const params = new URLSearchParams();\r\n      if (startDate) params.append('startDate', startDate);\r\n      if (endDate) params.append('endDate', endDate);\r\n\r\n      const url = `${API_BASE_URL}/statistics/tasks${params.toString() ? `?${params.toString()}` : ''}`;\r\n      const response = await fetch(url, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getTimeBlockStats: async (startDate?: string, endDate?: string) => {\r\n      const params = new URLSearchParams();\r\n      if (startDate) params.append('startDate', startDate);\r\n      if (endDate) params.append('endDate', endDate);\r\n\r\n      const url = `${API_BASE_URL}/statistics/time-blocks${params.toString() ? `?${params.toString()}` : ''}`;\r\n      const response = await fetch(url, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getProductivityStats: async (startDate?: string, endDate?: string) => {\r\n      const params = new URLSearchParams();\r\n      if (startDate) params.append('startDate', startDate);\r\n      if (endDate) params.append('endDate', endDate);\r\n\r\n      const url = `${API_BASE_URL}/statistics/productivity${params.toString() ? `?${params.toString()}` : ''}`;\r\n      const response = await fetch(url, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // USER ENDPOINTS\r\n  users: {\r\n    getProfile: async () => {\r\n      const response = await fetch(`${API_BASE_URL}/users/profile`, {\r\n        headers: getHeaders(),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n\r\n    updateProfile: async (updates: { name?: string; email?: string; avatar?: string }) => {\r\n      const response = await fetch(`${API_BASE_URL}/users/profile`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updates),\r\n      });\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n  // NOTIFICATIONS ENDPOINTS\r\n  notifications: {\r\n    subscribeEmail: async (subscriptionData: {\r\n      email: string;\r\n      taskReminders?: boolean;\r\n      dailySummary?: boolean;\r\n      weeklyReport?: boolean;\r\n      reminderHours?: number;\r\n    }) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscribe`, {\r\n        method: 'POST',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(subscriptionData),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    subscribeEmailPublic: async (subscriptionData: {\r\n      email: string;\r\n      name?: string;\r\n      taskReminders?: boolean;\r\n      dailySummary?: boolean;\r\n      weeklyReport?: boolean;\r\n      reminderHours?: number;\r\n    }) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscribe-public`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(subscriptionData),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    getSubscriptions: async () => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions`, {\r\n        headers: getHeaders(),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    updateSubscription: async (id: string, updateData: any) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions/${id}`, {\r\n        method: 'PUT',\r\n        headers: getHeaders(),\r\n        body: JSON.stringify(updateData),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    deleteSubscription: async (id: string) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions/${id}`, {\r\n        method: 'DELETE',\r\n        headers: getHeaders(),\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n\r\n    unsubscribe: async (token: string) => {\r\n      const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/unsubscribe?token=${token}`, {\r\n        method: 'POST',\r\n      }));\r\n      return handleResponse(response);\r\n    },\r\n  },\r\n\r\n\r\n};\r\n"], "names": [], "mappings": ";;;AAEA,oBAAoB;AACpB,MAAM,eAAe,iEAAmC;AAExD,8CAA8C;AAC9C,MAAM,iBAAiB,OAAO;IAC5B,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,SAAS,MAAM,CAAC,cAAc,EAAE,SAAS,UAAU,EAAE;QAE9G,iEAAiE;QACjE,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,QAAQ,GAAG,CAAC;YACZ,yBAAyB;YACzB,uCAAmC;;YAEnC;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;IACvF;IAEA,mCAAmC;IACnC,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;IACzC,IAAI,eAAe,YAAY,QAAQ,CAAC,qBAAqB;QAC3D,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,QAAQ,SAAS,eAAe,SAAS,UAAU,KAAK,IAAI,OAAO,IAAI;YACzE,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC;YACpB,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qBAAqB,YAAY,SAAS;gBACxD,MAAM,IAAI,MAAM;YAClB;QACF;QACA,OAAO;IACT;IAEA,kEAAkE;IAClE,OAAO;AACT;AAEA,mCAAmC;AACnC,MAAM,mBAAmB,OAAO;IAC9B,IAAI;QACF,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,oBAAoB;YAC3E,MAAM,IAAI,MAAM;QAClB;QACA,MAAM;IACR;AACF;AAEA,sEAAsE;AACtE,MAAM,2BAA2B,OAAO;IACtC,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,IAAI,SAAS,MAAM,KAAK,KAAK;YAC3B,0CAA0C;YAC1C,OAAO;QACT;QACA,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;IACvF;IAEA,mCAAmC;IACnC,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;IACzC,IAAI,eAAe,YAAY,QAAQ,CAAC,qBAAqB;QAC3D,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,QAAQ,SAAS,eAAe,SAAS,UAAU,KAAK,IAAI,OAAO,IAAI;YACzE,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC;YACpB,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,qBAAqB,YAAY,SAAS;gBACxD,MAAM,IAAI,MAAM;YAClB;QACF;QACA,OAAO;IACT;IAEA,kEAAkE;IAClE,OAAO;AACT;AAEA,qCAAqC;AACrC,MAAM,eAAe;IACnB,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEA,yCAAyC;AACzC,MAAM,aAAa;IACjB,MAAM,UAAuB;QAC3B,gBAAgB;IAClB;IAEA,MAAM,QAAQ;IACd,IAAI,OAAO;QACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC9C;IAEA,OAAO;AACT;AAEA,0DAA0D;AAC1D,MAAM,mBAAmB;IACvB,MAAM,UAAuB,CAAC;IAE9B,MAAM,QAAQ;IACd,IAAI,OAAO;QACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC9C;IAEA,OAAO;AACT;AAEO,MAAM,aAAa;IACxB,iBAAiB;IACjB,MAAM;QACJ,OAAO,OAAO,OAAe;YAC3B,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;gBAC1E,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,IAAI,KAAK,KAAK,EAAE;gBACd,aAAa,OAAO,CAAC,aAAa,KAAK,KAAK;YAC9C;YACA,OAAO;QACT;QAEA,UAAU,OAAO,MAAc,OAAe;YAC5C,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;oBAAO;gBAAS;YAC/C;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC;QAC1B;QAEA,gBAAgB;YACd,QAAQ,GAAG,CAAC;YACZ,MAAM,UAAU;YAChB,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,QAAQ,CAAC,EAAE;gBACvE,SAAS;YACX;YAEA,QAAQ,GAAG,CAAC,+CAA+C,SAAS,MAAM;YAC1E,MAAM,SAAS,MAAM,eAAe;YACpC,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,OAAO;QACL,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;gBACpD,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO,KAAK,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC9B,GAAG,IAAI;oBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;gBACzB,CAAC;QACH;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,SAAS;YACX;YACA,OAAO,yBAAyB;QAClC;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,kBAAkB,OAAO;YACvB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,GAAG,SAAS,CAAC,EAAE;gBACnE,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,qBAAqB;IACrB,YAAY;QACV,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;gBACzD,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,EAAE,IAAI,EAAE;gBAC/D,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,WAAW,CAAC,EAAE;gBACzD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,EAAE,IAAI,EAAE;gBAC/D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,EAAE,IAAI,EAAE;gBAC/D,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,uBAAuB;IACvB,YAAY;QACV,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO,KAAK,GAAG,CAAC,CAAC,YAAmB,CAAC;oBACnC,GAAG,SAAS;oBACZ,IAAI,UAAU,GAAG,IAAI,UAAU,EAAE;gBACnC,CAAC;QACH;QAEA,WAAW,OAAO;YAChB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,kBAAkB,EAAE,MAAM,EAAE;gBACvE,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO,KAAK,GAAG,CAAC,CAAC,YAAmB,CAAC;oBACnC,GAAG,SAAS;oBACZ,IAAI,UAAU,GAAG,IAAI,UAAU,EAAE;gBACnC,CAAC;QACH;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,IAAI,EAAE;gBAChE,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO;YAOb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO,IAAY;YACzB,QAAQ,GAAG,CAAC,2CAA2C,IAAI,YAAY;YACvE,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,IAAI,EAAE;gBAChE,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,IAAI,EAAE;gBAChE,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,kBAAkB,OAAO,IAAY;YACnC,QAAQ,GAAG,CAAC,8CAA8C,IAAI;YAC9D,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,EAAE,IAAI,EAAE;gBAChE,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAY;YACrC;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;IACF;IAEA,uBAAuB;IACvB,aAAa;QACX,KAAK;YACH,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,YAAY,CAAC,EAAE;gBAC1D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;IACF;IAEA,oBAAoB;IACpB,UAAU;QACR,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;gBACvD,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO,KAAK,GAAG,CAAC,CAAC,UAAiB,CAAC;oBACjC,GAAG,OAAO;oBACV,IAAI,QAAQ,GAAG,IAAI,QAAQ,EAAE;gBAC/B,CAAC;QACH;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;gBAC7D,SAAS;YACX;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO;YAIb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;gBACvD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;gBAC7D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,MAAM,OAAO,MAAM,eAAe;YAClC,2CAA2C;YAC3C,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,KAAK,GAAG,IAAI,KAAK,EAAE;YACzB;QACF;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI,EAAE;gBAC7D,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,iBAAiB;IACjB,OAAO;QACL,QAAQ;YACN,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;gBACpD,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,SAAS,OAAO;YACd,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;gBACpD,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO,IAAY;YACzB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO;YACb,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,OAAO,EAAE,IAAI,EAAE;gBAC1D,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,uBAAuB;IACvB,YAAY;QACV,cAAc,OAAO,WAAoB;YACvC,MAAM,SAAS,IAAI;YACnB,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,SAAS,OAAO,MAAM,CAAC,WAAW;YAEtC,MAAM,MAAM,GAAG,aAAa,iBAAiB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YACjG,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,mBAAmB,OAAO,WAAoB;YAC5C,MAAM,SAAS,IAAI;YACnB,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,SAAS,OAAO,MAAM,CAAC,WAAW;YAEtC,MAAM,MAAM,GAAG,aAAa,uBAAuB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YACvG,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,sBAAsB,OAAO,WAAoB;YAC/C,MAAM,SAAS,IAAI;YACnB,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,SAAS,OAAO,MAAM,CAAC,WAAW;YAEtC,MAAM,MAAM,GAAG,aAAa,wBAAwB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YACxG,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;YACX;YACA,OAAO,eAAe;QACxB;IACF;IAEA,iBAAiB;IACjB,OAAO;QACL,YAAY;YACV,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;gBAC5D,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,eAAe,OAAO;YACpB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;gBAC5D,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;IACF;IAEA,0BAA0B;IAC1B,eAAe;QACb,gBAAgB,OAAO;YAOrB,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,8BAA8B,CAAC,EAAE;gBAC7F,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,sBAAsB,OAAO;YAQ3B,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,qCAAqC,CAAC,EAAE;gBACpG,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,kBAAkB;YAChB,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,kCAAkC,CAAC,EAAE;gBACjG,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,oBAAoB,OAAO,IAAY;YACrC,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,mCAAmC,EAAE,IAAI,EAAE;gBACvG,QAAQ;gBACR,SAAS;gBACT,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,eAAe;QACxB;QAEA,oBAAoB,OAAO;YACzB,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,mCAAmC,EAAE,IAAI,EAAE;gBACvG,QAAQ;gBACR,SAAS;YACX;YACA,OAAO,eAAe;QACxB;QAEA,aAAa,OAAO;YAClB,MAAM,WAAW,MAAM,iBAAiB,MAAM,GAAG,aAAa,uCAAuC,EAAE,OAAO,EAAE;gBAC9G,QAAQ;YACV;YACA,OAAO,eAAe;QACxB;IACF;AAGF", "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/components/auth/auth-guard.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { ApiService } from '@/lib/services/api-service';\n\ninterface AuthGuardProps {\n  children: React.ReactNode;\n}\n\nexport function AuthGuard({ children }: AuthGuardProps) {\n  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);\n  const router = useRouter();\n\n  useEffect(() => {\n    const checkAuth = async () => {\n      console.log('AuthGuard: Bắt đầu kiểm tra authentication...');\n      const token = localStorage.getItem('authToken');\n      console.log('AuthGuard: Token từ localStorage:', token ? `${token.substring(0, 20)}...` : 'null');\n\n      if (!token) {\n        console.log('AuthGuard: Không có token, chuyển hướng đến login');\n        setIsAuthenticated(false);\n        router.push('/login');\n        return;\n      }\n\n      try {\n        console.log('AuthGuard: <PERSON>ang kiểm tra token với API...');\n        // Kiểm tra token có hợp lệ không\n        const user = await ApiService.auth.getCurrentUser();\n        console.log('AuthGuard: Token hợp lệ, user:', user);\n        setIsAuthenticated(true);\n      } catch (error) {\n        console.error('AuthGuard: Token không hợp lệ:', error);\n        localStorage.removeItem('authToken');\n        setIsAuthenticated(false);\n        router.push('/login');\n      }\n    };\n\n    checkAuth();\n  }, [router]);\n\n  // Đang kiểm tra authentication\n  if (isAuthenticated === null) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"></div>\n          <p className=\"mt-2 text-muted-foreground\">Đang kiểm tra đăng nhập...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Chưa đăng nhập\n  if (!isAuthenticated) {\n    return null; // Router sẽ chuyển hướng\n  }\n\n  // Đã đăng nhập\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,QAAQ,GAAG,CAAC;YACZ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,QAAQ,GAAG,CAAC,qCAAqC,QAAQ,GAAG,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;YAE1F,IAAI,CAAC,OAAO;gBACV,QAAQ,GAAG,CAAC;gBACZ,mBAAmB;gBACnB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,iCAAiC;gBACjC,MAAM,OAAO,MAAM,wIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,cAAc;gBACjD,QAAQ,GAAG,CAAC,kCAAkC;gBAC9C,mBAAmB;YACrB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,aAAa,UAAU,CAAC;gBACxB,mBAAmB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,+BAA+B;IAC/B,IAAI,oBAAoB,MAAM;QAC5B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,iBAAiB;IACjB,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,yBAAyB;IACxC;IAEA,eAAe;IACf,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/components/auth/auth-wrapper.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport { AuthGuard } from './auth-guard';\n\ninterface AuthWrapperProps {\n  children: React.ReactNode;\n}\n\n// Các trang không cần authentication - c<PERSON> thể truy cập tự do\nconst publicRoutes = [\n  '/login',\n  '/register',\n  '/', // Trang chủ\n  '/tasks', // Xem danh sách tasks (chỉ đọc)\n  '/projects', // Xem danh sách projects (chỉ đọc)\n  '/calendar', // Xem lịch (chỉ đọc)\n  '/statistics', // Xem thống kê (chỉ đọc)\n  '/categories', // Xem danh mục (chỉ đọc)\n  '/notes', // Xem ghi chú (chỉ đọc)\n];\n\n// Các trang cần authentication bắt buộc\nconst protectedRoutes = [\n  '/profile',\n  '/settings',\n];\n\nexport function AuthWrapper({ children }: AuthWrapperProps) {\n  const pathname = usePathname();\n\n  // Nếu là trang public, không cần AuthGuard\n  if (publicRoutes.includes(pathname)) {\n    return <>{children}</>;\n  }\n\n  // Nếu là trang protected, bắt buộc phải có AuthGuard\n  if (protectedRoutes.some(route => pathname.startsWith(route))) {\n    return (\n      <AuthGuard>\n        {children}\n      </AuthGuard>\n    );\n  }\n\n  // Các trang khác cũng có thể truy cập tự do (fallback)\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASA,6DAA6D;AAC7D,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,wCAAwC;AACxC,MAAM,kBAAkB;IACtB;IACA;CACD;AAEM,SAAS,YAAY,EAAE,QAAQ,EAAoB;IACxD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,2CAA2C;IAC3C,IAAI,aAAa,QAAQ,CAAC,WAAW;QACnC,qBAAO;sBAAG;;IACZ;IAEA,qDAAqD;IACrD,IAAI,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC,SAAS;QAC7D,qBACE,8OAAC,2IAAA,CAAA,YAAS;sBACP;;;;;;IAGP;IAEA,uDAAuD;IACvD,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 728, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/lib/contexts/statistics-context.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useCallback } from 'react';\n\ninterface StatisticsContextType {\n  refreshTrigger: number;\n  triggerRefresh: () => void;\n}\n\nconst StatisticsContext = createContext<StatisticsContextType | undefined>(undefined);\n\nexport function StatisticsProvider({ children }: { children: React.ReactNode }) {\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n\n  const triggerRefresh = useCallback(() => {\n    console.log('StatisticsContext: Triggering statistics refresh');\n    setRefreshTrigger(prev => prev + 1);\n  }, []);\n\n  return (\n    <StatisticsContext.Provider value={{ refreshTrigger, triggerRefresh }}>\n      {children}\n    </StatisticsContext.Provider>\n  );\n}\n\nexport function useStatistics() {\n  const context = useContext(StatisticsContext);\n  if (context === undefined) {\n    console.error('useStatistics must be used within a StatisticsProvider');\n    // Return default values instead of throwing error\n    return {\n      refreshTrigger: 0,\n      triggerRefresh: () => console.warn('StatisticsProvider not found')\n    };\n  }\n  return context;\n}\n\n// Hook để trigger refresh từ bất kỳ đâu\nexport function useStatisticsRefresh() {\n  const { triggerRefresh } = useStatistics();\n  return triggerRefresh;\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AASA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAqC;AAEpE,SAAS,mBAAmB,EAAE,QAAQ,EAAiC;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,QAAQ,GAAG,CAAC;QACZ,kBAAkB,CAAA,OAAQ,OAAO;IACnC,GAAG,EAAE;IAEL,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;YAAE;YAAgB;QAAe;kBACjE;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,QAAQ,KAAK,CAAC;QACd,kDAAkD;QAClD,OAAO;YACL,gBAAgB;YAChB,gBAAgB,IAAM,QAAQ,IAAI,CAAC;QACrC;IACF;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,cAAc,EAAE,GAAG;IAC3B,OAAO;AACT", "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/app/providers.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ThemeProvider } from \"next-themes\";\r\nimport { ReactNode } from \"react\";\r\nimport { AuthWrapper } from \"@/components/auth/auth-wrapper\";\r\nimport { StatisticsProvider } from \"@/lib/contexts/statistics-context\";\r\n\r\nexport function Providers({ children }: { children: ReactNode }) {\r\n  return (\r\n    <ThemeProvider\r\n      attribute=\"class\"\r\n      defaultTheme=\"system\"\r\n      enableSystem\r\n    >\r\n      <AuthWrapper>\r\n        <StatisticsProvider>\r\n          {children}\r\n        </StatisticsProvider>\r\n      </AuthWrapper>\r\n    </ThemeProvider>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAOO,SAAS,UAAU,EAAE,QAAQ,EAA2B;IAC7D,qBACE,8OAAC,gJAAA,CAAA,gBAAa;QACZ,WAAU;QACV,cAAa;QACb,YAAY;kBAEZ,cAAA,8OAAC,6IAAA,CAAA,cAAW;sBACV,cAAA,8OAAC,gJAAA,CAAA,qBAAkB;0BAChB;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/lib/utils/json-utils.ts"], "sourcesContent": ["/**\r\n * Utility functions for safe JSON operations\r\n */\r\n\r\n/**\r\n * Safely parse JSON string, returns null if parsing fails or input is invalid\r\n */\r\nexport const safeJsonParse = <T = any>(jsonString: string | null | undefined): T | null => {\r\n  if (!jsonString || jsonString === 'undefined' || jsonString === 'null' || jsonString.trim() === '') {\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    return JSON.parse(jsonString) as T;\r\n  } catch (error) {\r\n    console.warn('Failed to parse JSON:', error, 'Input:', jsonString);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Safely stringify object to JSON, returns empty string if fails\r\n */\r\nexport const safeJsonStringify = (obj: any): string => {\r\n  try {\r\n    return JSON.stringify(obj);\r\n  } catch (error) {\r\n    console.warn('Failed to stringify JSON:', error, 'Object:', obj);\r\n    return '';\r\n  }\r\n};\r\n\r\n/**\r\n * Safely get and parse item from localStorage\r\n */\r\nexport const safeLocalStorageGet = <T = any>(key: string): T | null => {\r\n  if (typeof window === 'undefined') {\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    const item = localStorage.getItem(key);\r\n    return safeJsonParse<T>(item);\r\n  } catch (error) {\r\n    console.warn('Failed to get from localStorage:', error, 'Key:', key);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Safely set item to localStorage\r\n */\r\nexport const safeLocalStorageSet = (key: string, value: any): boolean => {\r\n  if (typeof window === 'undefined') {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    const jsonString = safeJsonStringify(value);\r\n    if (jsonString) {\r\n      localStorage.setItem(key, jsonString);\r\n      return true;\r\n    }\r\n    return false;\r\n  } catch (error) {\r\n    console.warn('Failed to set to localStorage:', error, 'Key:', key, 'Value:', value);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Safely remove item from localStorage\r\n */\r\nexport const safeLocalStorageRemove = (key: string): boolean => {\r\n  if (typeof window === 'undefined') {\r\n    return false;\r\n  }\r\n\r\n  try {\r\n    localStorage.removeItem(key);\r\n    return true;\r\n  } catch (error) {\r\n    console.warn('Failed to remove from localStorage:', error, 'Key:', key);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Clear invalid localStorage items that contain 'undefined' or 'null' strings\r\n */\r\nexport const cleanupLocalStorage = (): number => {\r\n  if (typeof window === 'undefined') {\r\n    return 0;\r\n  }\r\n\r\n  let cleanedCount = 0;\r\n  const keysToRemove: string[] = [];\r\n\r\n  try {\r\n    for (let i = 0; i < localStorage.length; i++) {\r\n      const key = localStorage.key(i);\r\n      if (key) {\r\n        const value = localStorage.getItem(key);\r\n        if (value === 'undefined' || value === 'null' || value === '') {\r\n          keysToRemove.push(key);\r\n        }\r\n      }\r\n    }\r\n\r\n    keysToRemove.forEach(key => {\r\n      localStorage.removeItem(key);\r\n      cleanedCount++;\r\n    });\r\n\r\n    if (cleanedCount > 0) {\r\n      console.log(`Cleaned up ${cleanedCount} invalid localStorage items`);\r\n    }\r\n  } catch (error) {\r\n    console.warn('Failed to cleanup localStorage:', error);\r\n  }\r\n\r\n  return cleanedCount;\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;CAEC;;;;;;;;AACM,MAAM,gBAAgB,CAAU;IACrC,IAAI,CAAC,cAAc,eAAe,eAAe,eAAe,UAAU,WAAW,IAAI,OAAO,IAAI;QAClG,OAAO;IACT;IAEA,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,yBAAyB,OAAO,UAAU;QACvD,OAAO;IACT;AACF;AAKO,MAAM,oBAAoB,CAAC;IAChC,IAAI;QACF,OAAO,KAAK,SAAS,CAAC;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,6BAA6B,OAAO,WAAW;QAC5D,OAAO;IACT;AACF;AAKO,MAAM,sBAAsB,CAAU;IAC3C,wCAAmC;QACjC,OAAO;IACT;;AASF;AAKO,MAAM,sBAAsB,CAAC,KAAa;IAC/C,wCAAmC;QACjC,OAAO;IACT;;AAaF;AAKO,MAAM,yBAAyB,CAAC;IACrC,wCAAmC;QACjC,OAAO;IACT;;AASF;AAKO,MAAM,sBAAsB;IACjC,wCAAmC;QACjC,OAAO;IACT;;IAEA,IAAI;IACJ,MAAM;AA0BR", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/src/components/app-initializer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport { cleanupLocalStorage } from '@/lib/utils/json-utils';\r\n\r\n/**\r\n * Component để khởi tạo app và dọn dẹp dữ liệu không hợp lệ\r\n */\r\nexport function AppInitializer() {\r\n  useEffect(() => {\r\n    // Cleanup localStorage khi app khởi động\r\n    const cleanedCount = cleanupLocalStorage();\r\n\r\n    if (cleanedCount > 0) {\r\n      console.log(`AppInitializer: Đã dọn dẹp ${cleanedCount} mục localStorage không hợp lệ`);\r\n    }\r\n\r\n    // Global error handler cho unhandled promise rejections\r\n    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {\r\n      if (event.reason && event.reason.message) {\r\n        const message = event.reason.message;\r\n        if (message.includes('not valid JSON') || message.includes('\"undefined\" is not valid JSON')) {\r\n          console.warn('Caught JSON parsing error in promise:', event.reason);\r\n          event.preventDefault(); // Prevent the error from being logged to console\r\n          return;\r\n        }\r\n      }\r\n    };\r\n\r\n    // Global error handler cho window errors\r\n    const handleError = (event: ErrorEvent) => {\r\n      if (event.message) {\r\n        if (event.message.includes('not valid JSON') || event.message.includes('\"undefined\" is not valid JSON')) {\r\n          console.warn('Caught JSON parsing error:', event.message);\r\n          event.preventDefault();\r\n          return;\r\n        }\r\n      }\r\n    };\r\n\r\n    // Intercept console.error để filter các lỗi JSON parsing và empty src\r\n    const originalConsoleError = console.error;\r\n    console.error = (...args: any[]) => {\r\n      const message = args.join(' ');\r\n\r\n      // Suppress JSON parsing errors\r\n      if (message.includes('\"undefined\" is not valid JSON') || message.includes('not valid JSON')) {\r\n        console.warn('Suppressed JSON parsing error:', ...args);\r\n        return;\r\n      }\r\n\r\n      // Suppress empty src attribute warnings\r\n      if (message.includes('An empty string (\"\") was passed to the src attribute')) {\r\n        console.debug('Suppressed empty src warning:', ...args);\r\n        return;\r\n      }\r\n\r\n      // Call original console.error for other errors\r\n      originalConsoleError.apply(console, args);\r\n    };\r\n\r\n    // Add event listeners\r\n    window.addEventListener('unhandledrejection', handleUnhandledRejection);\r\n    window.addEventListener('error', handleError);\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      window.removeEventListener('unhandledrejection', handleUnhandledRejection);\r\n      window.removeEventListener('error', handleError);\r\n      // Restore original console.error\r\n      console.error = originalConsoleError;\r\n    };\r\n  }, []);\r\n\r\n  // Component này không render gì\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAQO,SAAS;IACd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,MAAM,eAAe,CAAA,GAAA,oIAAA,CAAA,sBAAmB,AAAD;QAEvC,IAAI,eAAe,GAAG;YACpB,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa,8BAA8B,CAAC;QACxF;QAEA,wDAAwD;QACxD,MAAM,2BAA2B,CAAC;YAChC,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,OAAO,EAAE;gBACxC,MAAM,UAAU,MAAM,MAAM,CAAC,OAAO;gBACpC,IAAI,QAAQ,QAAQ,CAAC,qBAAqB,QAAQ,QAAQ,CAAC,kCAAkC;oBAC3F,QAAQ,IAAI,CAAC,yCAAyC,MAAM,MAAM;oBAClE,MAAM,cAAc,IAAI,iDAAiD;oBACzE;gBACF;YACF;QACF;QAEA,yCAAyC;QACzC,MAAM,cAAc,CAAC;YACnB,IAAI,MAAM,OAAO,EAAE;gBACjB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBAAqB,MAAM,OAAO,CAAC,QAAQ,CAAC,kCAAkC;oBACvG,QAAQ,IAAI,CAAC,8BAA8B,MAAM,OAAO;oBACxD,MAAM,cAAc;oBACpB;gBACF;YACF;QACF;QAEA,sEAAsE;QACtE,MAAM,uBAAuB,QAAQ,KAAK;QAC1C,QAAQ,KAAK,GAAG,CAAC,GAAG;YAClB,MAAM,UAAU,KAAK,IAAI,CAAC;YAE1B,+BAA+B;YAC/B,IAAI,QAAQ,QAAQ,CAAC,oCAAoC,QAAQ,QAAQ,CAAC,mBAAmB;gBAC3F,QAAQ,IAAI,CAAC,qCAAqC;gBAClD;YACF;YAEA,wCAAwC;YACxC,IAAI,QAAQ,QAAQ,CAAC,yDAAyD;gBAC5E,QAAQ,KAAK,CAAC,oCAAoC;gBAClD;YACF;YAEA,+CAA+C;YAC/C,qBAAqB,KAAK,CAAC,SAAS;QACtC;QAEA,sBAAsB;QACtB,OAAO,gBAAgB,CAAC,sBAAsB;QAC9C,OAAO,gBAAgB,CAAC,SAAS;QAEjC,mBAAmB;QACnB,OAAO;YACL,OAAO,mBAAmB,CAAC,sBAAsB;YACjD,OAAO,mBAAmB,CAAC,SAAS;YACpC,iCAAiC;YACjC,QAAQ,KAAK,GAAG;QAClB;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n    } else if (process.env.TURBOPACK) {\n      module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n    } else {\n      module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAQzC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1CJ,OAAOC,OAAO,GAAGC,QAAQ;QAC3B,OAAO,IAAIL,QAAQC,GAAG,CAACO,SAAS,EAAE;;QAIlC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAqC,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,SAAS,eAAe,EAAC,IAAE;QAAC;QAAQ;KAAO;IAAC,SAAS,EAAE,CAAC;QAAE,CAAC,MAAM,OAAO,CAAC,KAAG,IAAE;YAAC;SAAE,EAAE,OAAO,CAAC,CAAA;YAAI,IAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG;YAAE,IAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE,EAAE,YAAY,CAAC,GAAE;QAAE,IAAG,EAAE;IAAE;IAAC,SAAS,EAAE,CAAC;QAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,KAAK,CAAC,WAAW,GAAC,CAAC;IAAC;IAAC,SAAS;QAAI,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAC,SAAO;IAAO;IAAC,IAAG,GAAE,EAAE;SAAQ,IAAG;QAAC,IAAI,IAAE,aAAa,OAAO,CAAC,MAAI,GAAE,IAAE,KAAG,MAAI,WAAS,MAAI;QAAE,EAAE;IAAE,EAAC,OAAM,GAAE,CAAC;AAAC;AAAE,IAAI,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,gCAA+B,IAAE,OAAO,UAAQ,aAAY,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,KAAK,IAAG,IAAE;IAAC,UAAS,CAAA,KAAI;IAAE,QAAO,EAAE;AAAA,GAAE,IAAE;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAY,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAA,GAAA,qMAAA,CAAA,aAAY,AAAD,EAAE,KAAG,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,qMAAA,CAAA,WAAU,EAAC,MAAK,EAAE,QAAQ,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,GAAG,CAAC;IAAA,IAAG,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,CAAC,EAAC,aAAY,CAAC,EAAC,2BAA0B,IAAE,CAAC,CAAC,EAAC,cAAa,IAAE,CAAC,CAAC,EAAC,mBAAkB,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,OAAO,EAAC,QAAO,IAAE,CAAC,EAAC,cAAa,IAAE,IAAE,WAAS,OAAO,EAAC,WAAU,IAAE,YAAY,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAU,AAAD,EAAE,IAAI,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAU,AAAD,EAAE,IAAI,MAAI,WAAS,MAAI,IAAG,IAAE,IAAE,OAAO,MAAM,CAAC,KAAG,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE;QAAE,IAAG,CAAC,GAAE;QAAO,MAAI,YAAU,KAAG,CAAC,IAAE,GAAG;QAAE,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,EAAE,KAAG,MAAK,IAAE,SAAS,eAAe,EAAC,IAAE,CAAA;YAAI,MAAI,UAAQ,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,KAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,IAAE,EAAE,UAAU,CAAC,YAAU,CAAC,IAAE,EAAE,YAAY,CAAC,GAAE,KAAG,EAAE,eAAe,CAAC,EAAE;QAAC;QAAE,IAAG,MAAM,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,KAAG,EAAE,IAAG,GAAE;YAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE,MAAK,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE;YAAE,EAAE,KAAK,CAAC,WAAW,GAAC;QAAC;QAAC,KAAG,QAAM;IAAG,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;QAAE,EAAE;QAAG,IAAG;YAAC,aAAa,OAAO,CAAC,GAAE;QAAE,EAAC,OAAM,GAAE,CAAC;IAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE;QAAG,EAAE,IAAG,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE;IAAS,GAAE;QAAC;QAAE;KAAE;IAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,OAAO,UAAU,CAAC;QAAG,OAAO,EAAE,WAAW,CAAC,IAAG,EAAE,IAAG,IAAI,EAAE,cAAc,CAAC;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,CAAA;YAAI,EAAE,GAAG,KAAG,KAAG,CAAC,EAAE,QAAQ,GAAC,EAAE,EAAE,QAAQ,IAAE,EAAE,EAAE;QAAC;QAAE,OAAO,OAAO,gBAAgB,CAAC,WAAU,IAAG,IAAI,OAAO,mBAAmB,CAAC,WAAU;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,EAAE,KAAG,OAAK,IAAE;IAAE,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC;YAAC,OAAM;YAAE,UAAS;YAAE,aAAY;YAAE,eAAc,MAAI,WAAS,IAAE;YAAE,QAAO,IAAE;mBAAI;gBAAE;aAAS,GAAC;YAAE,aAAY,IAAE,IAAE,KAAK;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,aAAY;QAAE,YAAW;QAAE,WAAU;QAAE,cAAa;QAAE,mBAAkB;QAAE,cAAa;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;QAAE,aAAY;IAAC,IAAG;AAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,OAAM,AAAD,EAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,mBAAkB,CAAC,EAAC,cAAa,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAI,IAAE,KAAK,SAAS,CAAC;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,EAAE,KAAK,CAAC,GAAE,CAAC;IAAG,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,UAAS;QAAC,GAAG,CAAC;QAAC,0BAAyB,CAAC;QAAE,OAAM,OAAO,UAAQ,cAAY,IAAE;QAAG,yBAAwB;YAAC,QAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAAA;IAAC;AAAE,IAAG,IAAE,CAAC,GAAE;IAAK,IAAG,GAAE;IAAO,IAAI;IAAE,IAAG;QAAC,IAAE,aAAa,OAAO,CAAC,MAAI,KAAK;IAAC,EAAC,OAAM,GAAE,CAAC;IAAC,OAAO,KAAG;AAAC,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,SAAS,aAAa,CAAC;IAAS,OAAO,KAAG,EAAE,YAAY,CAAC,SAAQ,IAAG,EAAE,WAAW,CAAC,SAAS,cAAc,CAAC,iLAAgL,SAAS,IAAI,CAAC,WAAW,CAAC,IAAG;QAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI,GAAE,WAAW;YAAK,SAAS,IAAI,CAAC,WAAW,CAAC;QAAE,GAAE;IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAC,KAAG,CAAC,IAAE,OAAO,UAAU,CAAC,EAAE,GAAE,EAAE,OAAO,GAAC,SAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].AppRouterContext\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].HooksClientContext\n"], "names": ["module", "exports", "require", "vendored", "HooksClientContext"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/router-reducer/reducers/get-segment-value.ts"], "sourcesContent": ["import type { Segment } from '../../../../server/app-render/types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n"], "names": ["getSegmentValue", "segment", "Array", "isArray"], "mappings": ";;;;+BAEgBA,mBAAAA;;;eAAAA;;;AAAT,SAASA,gBAAgBC,OAAgB;IAC9C,OAAOC,MAAMC,OAAO,CAACF,WAAWA,OAAO,CAAC,EAAE,GAAGA;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1195, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/shared/lib/segment.ts"], "sourcesContent": ["import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "addSearchParamsIfPageSegment", "isGroupSegment", "isParallelRouteSegment", "segment", "endsWith", "startsWith", "searchParams", "isPageSegment", "includes", "stringified<PERSON><PERSON>y", "JSON", "stringify"], "mappings": ";;;;;;;;;;;;;;;;;;IA4BaA,mBAAmB,EAAA;eAAnBA;;IADAC,gBAAgB,EAAA;eAAhBA;;IAhBGC,4BAA4B,EAAA;eAA5BA;;IATAC,cAAc,EAAA;eAAdA;;IAKAC,sBAAsB,EAAA;eAAtBA;;;AALT,SAASD,eAAeE,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQC,QAAQ,CAAC;AAChD;AAEO,SAASF,uBAAuBC,OAAe;IACpD,OAAOA,QAAQE,UAAU,CAAC,QAAQF,YAAY;AAChD;AAEO,SAASH,6BACdG,OAAgB,EAChBG,YAA2D;IAE3D,MAAMC,gBAAgBJ,QAAQK,QAAQ,CAACT;IAEvC,IAAIQ,eAAe;QACjB,MAAME,mBAAmBC,KAAKC,SAAS,CAACL;QACxC,OAAOG,qBAAqB,OACxBV,mBAAmB,MAAMU,mBACzBV;IACN;IAEA,OAAOI;AACT;AAEO,MAAMJ,mBAAmB;AACzB,MAAMD,sBAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1251, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1279, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["getRedirectError", "getRedirectStatusCodeFromError", "getRedirectTypeFromError", "getURLFromRedirectError", "permanentRedirect", "redirect", "actionAsyncStorage", "window", "require", "undefined", "url", "type", "statusCode", "RedirectStatusCode", "TemporaryRedirect", "error", "Error", "REDIRECT_ERROR_CODE", "digest", "getStore", "isAction", "RedirectType", "push", "replace", "PermanentRedirect", "isRedirectError", "split", "slice", "join", "Number", "at"], "mappings": ";;;;;;;;;;;;;;;;;;;IAegBA,gBAAgB,EAAA;eAAhBA;;IA6EAC,8BAA8B,EAAA;eAA9BA;;IARAC,wBAAwB,EAAA;eAAxBA;;IARAC,uBAAuB,EAAA;eAAvBA;;IAhBAC,iBAAiB,EAAA;eAAjBA;;IAvBAC,QAAQ,EAAA;eAARA;;;oCArCmB;+BAM5B;AAEP,MAAMC,qBACJ,OAAOC,WAAW,cAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAAST,iBACdU,GAAW,EACXC,IAAkB,EAClBC,UAAqE;IAArEA,IAAAA,eAAAA,KAAAA,GAAAA,aAAiCC,oBAAAA,kBAAkB,CAACC,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,MAAMC,eAAAA,mBAAmB,GAA7B,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CF,MAAMG,MAAM,GAAMD,eAAAA,mBAAmB,GAAC,MAAGN,OAAK,MAAGD,MAAI,MAAGE,aAAW;IACnE,OAAOG;AACT;AAcO,SAASV,SACd,2BAA2B,GAC3BK,GAAW,EACXC,IAAmB;QAEVL;IAATK,QAAAA,OAAAA,OAAAA,OAASL,CAAAA,sBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,+BAAAA,mBAAoBa,QAAQ,EAAA,KAAA,OAAA,KAAA,IAA5Bb,6BAAgCc,QAAQ,IAC7CC,eAAAA,YAAY,CAACC,IAAI,GACjBD,eAAAA,YAAY,CAACE,OAAO;IAExB,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACC,iBAAiB;AACxE;AAaO,SAASV,kBACd,2BAA2B,GAC3BM,GAAW,EACXC,IAAyC;IAAzCA,IAAAA,SAAAA,KAAAA,GAAAA,OAAqBU,eAAAA,YAAY,CAACE,OAAO;IAEzC,MAAMvB,iBAAiBU,KAAKC,MAAME,oBAAAA,kBAAkB,CAACW,iBAAiB;AACxE;AAUO,SAASrB,wBAAwBY,KAAc;IACpD,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAAS1B,yBAAyBa,KAAoB;IAC3D,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASzB,+BAA+Bc,KAAoB;IACjE,IAAI,CAACU,CAAAA,GAAAA,eAAAA,eAAe,EAACV,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOa,OAAOd,MAAMG,MAAM,CAACQ,KAAK,CAAC,KAAKI,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1433, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["notFound", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "error", "Error", "digest"], "mappings": ";;;;+BAsBgBA,YAAAA;;;eAAAA;;;oCAnBT;AAEP;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,4CAA4C;IAC5C,MAAMG,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["forbidden", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAqBgBA,aAAAA;;;eAAAA;;;oCAlBT;AAEP,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  // eslint-disable-next-line no-throw-literal\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["unauthorized", "DIGEST", "HTTP_ERROR_FALLBACK_ERROR_CODE", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;+BAsBgBA,gBAAAA;;;eAAAA;;;oCAnBT;AAEP,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAU,KAAEC,oBAAAA,8BAA8B,GAAC;AAE1C,SAASF;IACd,IAAI,CAACG,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,gHADG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4CAA4C;IAC5C,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1663, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BailoutToCSRError", "isBailoutToCSRError", "BAILOUT_TO_CSR", "Error", "constructor", "reason", "digest", "err"], "mappings": "AAAA,+GAA+G;;;;;;;;;;;;;;;;IAIlGA,iBAAiB,EAAA;eAAjBA;;IASGC,mBAAmB,EAAA;eAAnBA;;;AAZhB,MAAMC,iBAAiB;AAGhB,MAAMF,0BAA0BG;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAE,wCAAqCA,SAAAA,IAAAA,CADlBA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASJ;IAIzB;AACF;AAGO,SAASD,oBAAoBM,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1703, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/unstable-rethrow.browser.ts"], "sourcesContent": ["import { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (isNextRouterError(error) || isBailoutToCSRError(error)) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "Error", "cause"], "mappings": ";;;;+BAGgBA,oBAAAA;;;eAAAA;;;8BAHoB;mCACF;AAE3B,SAASA,iBAAiBC,KAAc;IAC7C,IAAIC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAAUE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,QAAQ;QAC1D,MAAMA;IACR;IAEA,IAAIA,iBAAiBG,SAAS,WAAWH,OAAO;QAC9CD,iBAAiBC,MAAMI,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1762, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  const hangingPromise = new Promise<T>((_, reject) => {\n    signal.addEventListener(\n      'abort',\n      () => {\n        reject(new HangingPromiseRejectionError(expression))\n      },\n      { once: true }\n    )\n  })\n  // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n  // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n  // your own promise out of it you'll need to ensure you handle the error when it rejects.\n  hangingPromise.catch(ignoreReject)\n  return hangingPromise\n}\n\nfunction ignoreReject() {}\n"], "names": ["isHangingPromiseRejectionError", "makeHangingPromise", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "expression", "signal", "hanging<PERSON>romise", "Promise", "_", "reject", "addEventListener", "once", "catch", "ignoreReject"], "mappings": ";;;;;;;;;;;;;;;IAAgBA,8BAA8B,EAAA;eAA9BA;;IA6BAC,kBAAkB,EAAA;eAAlBA;;;AA7BT,SAASD,+BACdE,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YAA4BC,UAAkB,CAAE;QAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,qJAAqJ,CAAC,GAAA,IAAA,CAFnRA,UAAAA,GAAAA,YAAAA,IAAAA,CAFZL,MAAAA,GAASC;IAMzB;AACF;AASO,SAASH,mBACdQ,MAAmB,EACnBD,UAAkB;IAElB,MAAME,iBAAiB,IAAIC,QAAW,CAACC,GAAGC;QACxCJ,OAAOK,gBAAgB,CACrB,SACA;YACED,OAAO,IAAIR,6BAA6BG;QAC1C,GACA;YAAEO,MAAM;QAAK;IAEjB;IACA,2GAA2G;IAC3G,6GAA6G;IAC7G,yFAAyF;IACzFL,eAAeM,KAAK,CAACC;IACrB,OAAOP;AACT;AAEA,SAASO,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1816, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["isPostpone", "REACT_POSTPONE_TYPE", "Symbol", "for", "error", "$$typeof"], "mappings": ";;;;+BAEgBA,cAAAA;;;eAAAA;;;AAFhB,MAAMC,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASH,WAAWI,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKJ;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1835, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DynamicServerError", "isDynamicServerError", "DYNAMIC_ERROR_CODE", "Error", "constructor", "description", "digest", "err"], "mappings": ";;;;;;;;;;;;;;;IAEaA,kBAAkB,EAAA;eAAlBA;;IAQGC,oBAAoB,EAAA;eAApBA;;;AAVhB,MAAMC,qBAAqB;AAEpB,MAAMF,2BAA2BG;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAE,2BAAwBA,cAAAA,IAAAA,CADLA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCJ;IAIpC;AACF;AAEO,SAASD,qBAAqBM,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAID,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOC,IAAID,MAAM,KAAKJ;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["StaticGenBailoutError", "isStaticGenBailoutError", "NEXT_STATIC_GEN_BAILOUT", "Error", "code", "error"], "mappings": ";;;;;;;;;;;;;;;IAEaA,qBAAqB,EAAA;eAArBA;;IAIGC,uBAAuB,EAAA;eAAvBA;;;AANhB,MAAMC,0BAA0B;AAEzB,MAAMF,8BAA8BG;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOF;;AACzB;AAEO,SAASD,wBACdI,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMD,IAAI,KAAKF;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1927, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/lib/metadata/metadata-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;;;;;;;IAAaA,sBAAsB,EAAA;eAAtBA;;IAEAC,oBAAoB,EAAA;eAApBA;;IADAC,sBAAsB,EAAA;eAAtBA;;;AADN,MAAMF,yBAAyB;AAC/B,MAAME,yBAAyB;AAC/B,MAAMD,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1961, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["atLeastOneTask", "scheduleImmediate", "scheduleOnNextTick", "waitAtLeastOneReactRenderTask", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "setImmediate", "r"], "mappings": ";;;;;;;;;;;;;;;;;IA4CgBA,cAAc,EAAA;eAAdA;;IAbHC,iBAAiB,EAAA;eAAjBA;;IAtBAC,kBAAkB,EAAA;eAAlBA;;IAgDGC,6BAA6B,EAAA;eAA7BA;;;AAhDT,MAAMD,qBAAqB,CAAWE;IAC3C,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;QAEzC,OAAO;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF;AAQO,MAAMH,oBAAoB,CAAWG;IAC1C,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACLG,aAAaT;IACf;AACF;AAOO,SAASJ;IACd,OAAO,IAAIK,QAAc,CAACC,UAAYL,kBAAkBK;AAC1D;AAWO,SAASH;IACd,IAAIK,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;IAEzC,OAAO;QACL,OAAO,IAAIL,QAAQ,CAACS,IAAMD,aAAaC;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  return abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      if (prerenderStore.validating === true) {\n        // We always log Request Access in dev at the point of calling the function\n        // So we mark the dynamic validation as not requiring it to be printed\n        dynamicTracking.syncDynamicLogged = true\n      }\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n"], "names": ["Postpone", "abortAndThrowOnSynchronousRequestDataAccess", "abortOnSynchronousPlatformIOAccess", "accessedDynamicData", "annotateDynamicAccess", "consumeDynamicAccess", "createDynamicTrackingState", "createDynamicValidationState", "createHangingInputAbortSignal", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "getFirstDynamicReason", "isDynamicPostpone", "isPrerenderInterruptedError", "markCurrentScopeAsDynamic", "postponeWithTracking", "throwIfDisallowedDynamic", "throwToInterruptStaticGeneration", "trackAllowedDynamicAccess", "trackDynamicDataInDynamicRender", "trackFallbackParamAccessed", "trackSynchronousPlatformIOAccessInDev", "trackSynchronousRequestDataAccessInDev", "useDynamicRouteParams", "hasPostpone", "React", "unstable_postpone", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "undefined", "syncDynamicErrorWithStack", "hasSuspendedDynamic", "hasDynamicMetadata", "hasDynamicViewport", "hasSyncDynamicErrors", "dynamicErrors", "trackingState", "expression", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "StaticGenBailoutError", "route", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "prerenderStore", "workUnitAsyncStorage", "getStore", "_store", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "errorWithStack", "requestStore", "prerenderPhase", "validating", "syncDynamicLogged", "assertPostpone", "createPostponeReason", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "length", "serverDynamic", "clientDynamic", "filter", "access", "map", "split", "slice", "line", "join", "AbortController", "x", "signal", "cacheSignal", "inputReady", "then", "scheduleOnNextTick", "workStore", "workAsyncStorage", "isStaticGeneration", "fallbackRouteParams", "size", "use", "makeHangingPromise", "renderSignal", "hasSuspenseRegex", "hasMetadataRegex", "RegExp", "METADATA_BOUNDARY_NAME", "hasViewportRegex", "VIEWPORT_BOUNDARY_NAME", "hasOutletRegex", "OUTLET_BOUNDARY_NAME", "componentStack", "dynamicValidation", "test", "createErrorWithComponentStack", "syncError", "syncExpression", "syncLogged", "console", "i"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4UeA,QAAQ,EAAA;eAARA;;IAnCAC,2CAA2C,EAAA;eAA3CA;;IAlCAC,kCAAkC,EAAA;eAAlCA;;IA+JAC,mBAAmB,EAAA;eAAnBA;;IA4GAC,qBAAqB,EAAA;eAArBA;;IAtGAC,oBAAoB,EAAA;eAApBA;;IAxWAC,0BAA0B,EAAA;eAA1BA;;IAWAC,4BAA4B,EAAA;eAA5BA;;IA2aAC,6BAA6B,EAAA;eAA7BA;;IAjBAC,0BAA0B,EAAA;eAA1BA;;IAlDAC,wBAAwB,EAAA;eAAxBA;;IA9VAC,qBAAqB,EAAA;eAArBA;;IAwRAC,iBAAiB,EAAA;eAAjBA;;IAwCAC,2BAA2B,EAAA;eAA3BA;;IAnTAC,yBAAyB,EAAA;eAAzBA;;IA+OAC,oBAAoB,EAAA;eAApBA;;IAgSAC,wBAAwB,EAAA;eAAxBA;;IA/bAC,gCAAgC,EAAA;eAAhCA;;IAqZAC,yBAAyB,EAAA;eAAzBA;;IA5XAC,+BAA+B,EAAA;eAA/BA;;IAzCAC,0BAA0B,EAAA;eAA1BA;;IAiHAC,qCAAqC,EAAA;eAArCA;;IA2CHC,sCAAsC,EAAA;eAAtCA;;IA+NGC,qBAAqB,EAAA;eAArBA;;;8DAthBE;oCAEiB;yCACG;8CACD;0CACJ;uCACE;mCAK5B;2BAC4B;;;;;;AAEnC,MAAMC,cAAc,OAAOC,OAAAA,OAAK,CAACC,iBAAiB,KAAK;AA2ChD,SAASpB,2BACdqB,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,uBAAuBC;QACvBC,2BAA2B;IAC7B;AACF;AAEO,SAASxB;IACd,OAAO;QACLyB,qBAAqB;QACrBC,oBAAoB;QACpBC,oBAAoB;QACpBC,sBAAsB;QACtBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASzB,sBACd0B,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASxB,0BACdyB,KAAgB,EAChBC,aAAuE,EACvEF,UAAkB;IAElB,IAAIE,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAIF,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEN,MAAMO,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIE,eAAe;QACjB,IAAIA,cAAcC,IAAI,KAAK,iBAAiB;YAC1C1B,qBACEwB,MAAMO,KAAK,EACXR,YACAE,cAAcO,eAAe;QAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;YACpDD,cAAcQ,UAAU,GAAG;YAE3B,uGAAuG;YACvG,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,MAAMY,uBAAuB,GAAGb;YAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;YAEnC,MAAMJ;QACR,OAAO,IACLK,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,iBACAA,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAUO,SAASrC,2BACdmB,KAAgB,EAChBD,UAAkB;IAElB,MAAMoB,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,IAAI,CAACF,kBAAkBA,eAAejB,IAAI,KAAK,iBAAiB;IAEhE1B,qBAAqBwB,MAAMO,KAAK,EAAER,YAAYoB,eAAeX,eAAe;AAC9E;AAQO,SAAS9B,iCACdqB,UAAkB,EAClBC,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIC,oBAAAA,kBAAkB,CAChC,CAAC,MAAM,EAAEX,MAAMO,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAoB,eAAeV,UAAU,GAAG;IAE5BT,MAAMY,uBAAuB,GAAGb;IAChCC,MAAMa,iBAAiB,GAAGH,IAAII,KAAK;IAEnC,MAAMJ;AACR;AASO,SAAS9B,gCACd0C,MAAiB,EACjBrB,aAAmC;IAEnC,IAAIA,eAAe;QACjB,IACEA,cAAcC,IAAI,KAAK,WACvBD,cAAcC,IAAI,KAAK,kBACvB;YACA,6FAA6F;YAC7F,iGAAiG;YACjG,kCAAkC;YAClC;QACF;QACA,IACED,cAAcC,IAAI,KAAK,eACvBD,cAAcC,IAAI,KAAK,oBACvB;YACAD,cAAcQ,UAAU,GAAG;QAC7B;QACA,IACEM,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBhB,cAAcC,IAAI,KAAK,WACvB;YACAD,cAAciB,WAAW,GAAG;QAC9B;IACF;AACF;AAEA,yFAAyF;AACzF,kGAAkG;AAClG,qEAAqE;AACrE,SAASK,oCACPhB,KAAa,EACbR,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMK,SAAS,CAAC,MAAM,EAAEjB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAM0B,QAAQC,gCAAgCF;IAE9CL,eAAeQ,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMjB,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASpC,mCACd4C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;QAC9C;IACF;IACA,OAAOR,oCAAoChB,OAAOR,YAAYoB;AAChE;AAEO,SAASrC,sCACdkD,YAA0B;IAE1B,oFAAoF;IACpF,oDAAoD;IACpDA,aAAaC,cAAc,GAAG;AAChC;AAYO,SAASvE,4CACd6C,KAAa,EACbR,UAAkB,EAClBgC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnB,IAAIA,gBAAgBhB,yBAAyB,KAAK,MAAM;YACtDgB,gBAAgBlB,qBAAqB,GAAGS;YACxCS,gBAAgBhB,yBAAyB,GAAGuC;YAC5C,IAAIZ,eAAee,UAAU,KAAK,MAAM;gBACtC,2EAA2E;gBAC3E,sEAAsE;gBACtE1B,gBAAgB2B,iBAAiB,GAAG;YACtC;QACF;IACF;IACAZ,oCAAoChB,OAAOR,YAAYoB;IACvD,MAAMO,gCACJ,CAAC,MAAM,EAAEnB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AAGO,MAAMhB,yCACXD;AASK,SAASrB,SAAS,EAAE+D,MAAM,EAAEjB,KAAK,EAAiB;IACvD,MAAMY,iBAAiBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACpD,MAAMb,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACNhC,qBAAqB+B,OAAOiB,QAAQhB;AACtC;AAEO,SAAShC,qBACd+B,KAAa,EACbR,UAAkB,EAClBS,eAA4C;IAE5C4B;IACA,IAAI5B,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACff,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;IAEAb,OAAAA,OAAK,CAACC,iBAAiB,CAACkD,qBAAqB9B,OAAOR;AACtD;AAEA,SAASsC,qBAAqB9B,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS1B,kBAAkBqC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAY4B,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyB7B,IAAY4B,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBf,MAAc;IAC7C,OACEA,OAAOgB,QAAQ,CACb,sEAEFhB,OAAOgB,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBF,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIP,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMW,6BAA6B;AAEnC,SAASf,gCAAgCY,OAAe;IACtD,MAAMb,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMQ,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7Bb,MAAciB,MAAM,GAAGD;IACzB,OAAOhB;AACT;AAMO,SAASnD,4BACdmD,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAciB,MAAM,KAAKD,8BAC1B,UAAUhB,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASlE,oBACdyB,eAAqC;IAErC,OAAOA,gBAAgBsD,MAAM,GAAG;AAClC;AAEO,SAAS7E,qBACd8E,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcvD,eAAe,CAACwC,IAAI,IAAIgB,cAAcxD,eAAe;IACnE,OAAOuD,cAAcvD,eAAe;AACtC;AAEO,SAASlB,yBACdkB,eAAqC;IAErC,OAAOA,gBACJyD,MAAM,CACL,CAACC,SACC,OAAOA,OAAOjC,KAAK,KAAK,YAAYiC,OAAOjC,KAAK,CAAC6B,MAAM,GAAG,GAE7DK,GAAG,CAAC,CAAC,EAAEjD,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLmC,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKX,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIW,KAAKX,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIW,KAAKX,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCY,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAErD,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAASsB;IACP,IAAI,CAACnD,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI6C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAAS5D,2BAA2BsD,MAAc;IACvDY;IACA,MAAMT,aAAa,IAAI0B;IACvB,qFAAqF;IACrF,IAAI;QACFnE,OAAAA,OAAK,CAACC,iBAAiB,CAACqC;IAC1B,EAAE,OAAO8B,GAAY;QACnB3B,WAAWC,KAAK,CAAC0B;IACnB;IACA,OAAO3B,WAAW4B,MAAM;AAC1B;AAOO,SAAStF,8BACdgC,aAAmC;IAEnC,MAAM0B,aAAa,IAAI0B;IAEvB,IAAIpD,cAAcuD,WAAW,EAAE;QAC7B,gFAAgF;QAChF,mFAAmF;QACnF,uCAAuC;QACvCvD,cAAcuD,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;YAC1C/B,WAAWC,KAAK;QAClB;IACF,OAAO;QACL,gFAAgF;QAChF,kFAAkF;QAClF,gFAAgF;QAChF,+EAA+E;QAC/E,0DAA0D;QAC1D+B,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,IAAMhC,WAAWC,KAAK;IAC3C;IAEA,OAAOD,WAAW4B,MAAM;AAC1B;AAEO,SAAS1F,sBACdkC,UAAkB,EAClBoB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBnB,eAAe,CAACwC,IAAI,CAAC;YACnCf,OAAON,gBAAgBpB,sBAAsB,GACzC,IAAI0C,QAAQhB,KAAK,GACjBvB;YACJQ;QACF;IACF;AACF;AAEO,SAASf,sBAAsBe,UAAkB;IACtD,MAAM6D,YAAYC,0BAAAA,gBAAgB,CAACxC,QAAQ;IAE3C,IACEuC,aACAA,UAAUE,kBAAkB,IAC5BF,UAAUG,mBAAmB,IAC7BH,UAAUG,mBAAmB,CAACC,IAAI,GAAG,GACrC;QACA,oEAAoE;QACpE,YAAY;QACZ,MAAM/D,gBAAgBmB,8BAAAA,oBAAoB,CAACC,QAAQ;QACnD,IAAIpB,eAAe;YACjB,mDAAmD;YACnD,IAAIA,cAAcC,IAAI,KAAK,aAAa;gBACtC,iDAAiD;gBACjD,6EAA6E;gBAC7E,uDAAuD;gBACvDhB,OAAAA,OAAK,CAAC+E,GAAG,CAACC,CAAAA,GAAAA,uBAAAA,kBAAkB,EAACjE,cAAckE,YAAY,EAAEpE;YAC3D,OAAO,IAAIE,cAAcC,IAAI,KAAK,iBAAiB;gBACjD,8BAA8B;gBAC9B1B,qBACEoF,UAAUrD,KAAK,EACfR,YACAE,cAAcO,eAAe;YAEjC,OAAO,IAAIP,cAAcC,IAAI,KAAK,oBAAoB;gBACpDxB,iCAAiCqB,YAAY6D,WAAW3D;YAC1D;QACF;IACF;AACF;AAEA,MAAMmE,mBAAmB;AACzB,MAAMC,mBAAmB,IAAIC,OAC3B,CAAC,UAAU,EAAEC,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAEG,mBAAAA,sBAAsB,CAAC,QAAQ,CAAC;AAE/C,MAAMC,iBAAiB,IAAIJ,OAAO,CAAC,UAAU,EAAEK,mBAAAA,oBAAoB,CAAC,QAAQ,CAAC;AAEtE,SAAShG,0BACd4B,KAAa,EACbqE,cAAsB,EACtBC,iBAAyC,EACzCjC,aAAmC,EACnCC,aAAmC;IAEnC,IAAI6B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIP,iBAAiBS,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBnF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI8E,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBlF,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAIyE,iBAAiBU,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBpF,mBAAmB,GAAG;QACxC;IACF,OAAO,IACLmD,cAAcpD,yBAAyB,IACvCqD,cAAcrD,yBAAyB,EACvC;QACAqF,kBAAkBjF,oBAAoB,GAAG;QACzC;IACF,OAAO;QACL,MAAM0C,UAAU,CAAC,OAAO,EAAE/B,MAAM,+UAA+U,CAAC;QAChX,MAAMkB,QAAQsD,8BAA8BzC,SAASsC;QACrDC,kBAAkBhF,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA,SAASsD,8BACPzC,OAAe,EACfsC,cAAsB;IAEtB,MAAMnD,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMQ,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/Bb,MAAMX,KAAK,GAAG,YAAYwB,UAAUsC;IACpC,OAAOnD;AACT;AAEO,SAAShD,yBACd8B,KAAa,EACbsE,iBAAyC,EACzCjC,aAAmC,EACnCC,aAAmC;IAEnC,IAAImC;IACJ,IAAIC;IACJ,IAAIC;IACJ,IAAItC,cAAcpD,yBAAyB,EAAE;QAC3CwF,YAAYpC,cAAcpD,yBAAyB;QACnDyF,iBAAiBrC,cAActD,qBAAqB;QACpD4F,aAAatC,cAAcT,iBAAiB,KAAK;IACnD,OAAO,IAAIU,cAAcrD,yBAAyB,EAAE;QAClDwF,YAAYnC,cAAcrD,yBAAyB;QACnDyF,iBAAiBpC,cAAcvD,qBAAqB;QACpD4F,aAAarC,cAAcV,iBAAiB,KAAK;IACnD,OAAO;QACL6C,YAAY;QACZC,iBAAiB1F;QACjB2F,aAAa;IACf;IAEA,IAAIL,kBAAkBjF,oBAAoB,IAAIoF,WAAW;QACvD,IAAI,CAACE,YAAY;YACf,8FAA8F;YAC9F,8DAA8D;YAC9DC,QAAQ1D,KAAK,CAACuD;QAChB;QACA,wEAAwE;QACxE,MAAM,IAAI1E,yBAAAA,qBAAqB;IACjC;IAEA,MAAMT,gBAAgBgF,kBAAkBhF,aAAa;IACrD,IAAIA,cAAc8C,MAAM,EAAE;QACxB,IAAK,IAAIyC,IAAI,GAAGA,IAAIvF,cAAc8C,MAAM,EAAEyC,IAAK;YAC7CD,QAAQ1D,KAAK,CAAC5B,aAAa,CAACuF,EAAE;QAChC;QAEA,MAAM,IAAI9E,yBAAAA,qBAAqB;IACjC;IAEA,IAAI,CAACuE,kBAAkBpF,mBAAmB,EAAE;QAC1C,IAAIoF,kBAAkBnF,kBAAkB,EAAE;YACxC,IAAIsF,WAAW;gBACbG,QAAQ1D,KAAK,CAACuD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI1E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE0E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI3E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,OAAO,IAAIsE,kBAAkBlF,kBAAkB,EAAE;YAC/C,IAAIqF,WAAW;gBACbG,QAAQ1D,KAAK,CAACuD;gBACd,MAAM,OAAA,cAEL,CAFK,IAAI1E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,oEAAoE,EAAE0E,eAAe,+EAA+E,CAAC,GADjL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAM,OAAA,cAEL,CAFK,IAAI3E,yBAAAA,qBAAqB,CAC7B,CAAC,OAAO,EAAEC,MAAM,8cAA8c,CAAC,GAD3d,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2572, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport { isDynamicPostpone } from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["unstable_rethrow", "error", "isNextRouterError", "isBailoutToCSRError", "isDynamicServerError", "isDynamicPostpone", "isPostpone", "isHangingPromiseRejectionError", "Error", "cause"], "mappings": ";;;;+BAOg<PERSON>,oBAAAA;;;eAAAA;;;uCAP+B;4BACpB;8BACS;mCACF;kCACA;oCACG;AAE9B,SAASA,iBAAiBC,KAAc;IAC7C,IACEC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACD,UAClBE,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,UACpBG,CAAAA,GAAAA,oBAAAA,oBAAoB,EAACH,UACrBI,CAAAA,GAAAA,kBAAAA,iBAAiB,EAACJ,UAClBK,CAAAA,GAAAA,YAAAA,UAAU,EAACL,UACXM,CAAAA,GAAAA,uBAAAA,8BAA8B,EAACN,QAC/B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBO,SAAS,WAAWP,OAAO;QAC9CD,iBAAiBC,MAAMQ,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2608, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC,GAAA;;;;+BACY<PERSON>,oBAAAA;;;eAAAA;;;AAAN,MAAMA,mBACX,OAAOC,WAAW,cAEZC,QAAQ,wHACRF,gBAAgB,GAEhBE,QAAQ,yHACRF,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2637, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/navigation.react-server.ts"], "sourcesContent": ["/** @internal */\nclass ReadonlyURLSearchParamsError extends <PERSON>rror {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\nclass ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "ReadonlyURLSearchParamsError", "Error", "constructor", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA,cAAc,GAAA;;;;;;;;;;;;;;;;;;;;;IAkCLA,uBAAuB,EAAA;eAAvBA;;IALAC,YAAY,EAAA;eAAZA,eAAAA,YAAY;;IAEZC,SAAS,EAAA;eAATA,WAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAFEC,iBAAiB,EAAA;eAAjBA,UAAAA,iBAAiB;;IAA3BC,QAAQ,EAAA;eAARA,UAAAA,QAAQ;;IAIRC,YAAY,EAAA;eAAZA,cAAAA,YAAY;;IACZC,gBAAgB,EAAA;eAAhBA,iBAAAA,gBAAgB;;;0BALmB;+BACf;0BACJ;2BACC;8BACG;iCACI;AAhCjC,MAAMC,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAEA,MAAMV,gCAAgCW;IACpC,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIJ;IACZ;IACA,wKAAwK,GACxKK,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,MAAM;QACJ,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,OAAO;QACL,MAAM,IAAIP;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2720, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'contexts'\n].ServerInsertedHtml\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedHtml"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,WACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2727, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/bailout-to-client-rendering.ts"], "sourcesContent": ["import { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { workAsyncStorage } from '../../server/app-render/work-async-storage.external'\n\nexport function bailoutToClientRendering(reason: string): void | never {\n  const workStore = workAsyncStorage.getStore()\n\n  if (workStore?.forceStatic) return\n\n  if (workStore?.isStaticGeneration) throw new BailoutToCSRError(reason)\n}\n"], "names": ["bailoutToClientRendering", "reason", "workStore", "workAsyncStorage", "getStore", "forceStatic", "isStaticGeneration", "BailoutToCSRError"], "mappings": ";;;;+BAGgBA,4BAAAA;;;eAAAA;;;8BAHkB;0CACD;AAE1B,SAASA,yBAAyBC,MAAc;IACrD,MAAMC,YAAYC,0BAAAA,gBAAgB,CAACC,QAAQ;IAE3C,IAAIF,aAAAA,OAAAA,KAAAA,IAAAA,UAAWG,WAAW,EAAE;IAE5B,IAAIH,aAAAA,OAAAA,KAAAA,IAAAA,UAAWI,kBAAkB,EAAE,MAAM,OAAA,cAA6B,CAA7B,IAAIC,cAAAA,iBAAiB,CAACN,SAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAA4B;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2760, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/src/client/components/navigation.ts"], "sourcesContent": ["import type { Flight<PERSON>outerState } from '../../server/app-render/types'\nimport type { Params } from '../../server/request/params'\n\nimport { useContext, useMemo } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getSegmentValue } from './router-reducer/reducers/get-segment-value'\nimport { PAGE_SEGMENT_KEY, DEFAULT_SEGMENT_KEY } from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './navigation.react-server'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { bailoutToClientRendering } =\n      require('./bailout-to-client-rendering') as typeof import('./bailout-to-client-rendering')\n    // TODO-APP: handle dynamic = 'force-static' here and on the client\n    bailoutToClientRendering('useSearchParams()')\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  return useContext(PathnameContext) as string\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  return useContext(PathParamsContext) as T\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\n// Client components API\nfunction getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  if (!selectedLayoutSegments || selectedLayoutSegments.length === 0) {\n    return null\n  }\n\n  const selectedLayoutSegment =\n    parallelRouteKey === 'children'\n      ? selectedLayoutSegments[0]\n      : selectedLayoutSegments[selectedLayoutSegments.length - 1]\n\n  // if the default slot is showing, we return null since it's not technically \"selected\" (it's a fallback)\n  // and returning an internal value like `__DEFAULT__` would be confusing.\n  return selectedLayoutSegment === DEFAULT_SEGMENT_KEY\n    ? null\n    : selectedLayoutSegment\n}\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["ReadonlyURLSearchParams", "RedirectType", "ServerInsertedHTMLContext", "forbidden", "notFound", "permanentRedirect", "redirect", "unauthorized", "unstable_rethrow", "useParams", "usePathname", "useRouter", "useSearchParams", "useSelectedLayoutSegment", "useSelectedLayoutSegments", "useServerInsertedHTML", "useDynamicRouteParams", "window", "require", "undefined", "searchParams", "useContext", "SearchParamsContext", "readonlySearchParams", "useMemo", "bailoutToClientRendering", "PathnameContext", "router", "AppRouterContext", "Error", "PathParamsContext", "getSelectedLayoutSegmentPath", "tree", "parallelRouteKey", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segment", "segmentValue", "getSegmentValue", "startsWith", "PAGE_SEGMENT_KEY", "push", "context", "LayoutRouterContext", "parentTree", "selectedLayoutSegments", "length", "selectedLayoutSegment", "DEFAULT_SEGMENT_KEY"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0REA,uBAAuB,EAAA;eAAvBA,uBAAAA,uBAAuB;;IADvBC,YAAY,EAAA;eAAZA,uBAAAA,YAAY;;IApLZC,yBAAyB,EAAA;eAAzBA,iCAAAA,yBAAyB;;IAgLzBC,SAAS,EAAA;eAATA,uBAAAA,SAAS;;IADTC,QAAQ,EAAA;eAARA,uBAAAA,QAAQ;;IAIRC,iBAAiB,EAAA;eAAjBA,uBAAAA,iBAAiB;;IADjBC,QAAQ,EAAA;eAARA,uBAAAA,QAAQ;;IADRC,YAAY,EAAA;eAAZA,uBAAAA,YAAY;;IAKZC,gBAAgB,EAAA;eAAhBA,uBAAAA,gBAAgB;;IApIFC,SAAS,EAAA;eAATA;;IA5DAC,WAAW,EAAA;eAAXA;;IAiCAC,SAAS,EAAA;eAATA;;IA9EAC,eAAe,EAAA;eAAfA;;IA6MAC,wBAAwB,EAAA;eAAxBA;;IA/BAC,yBAAyB,EAAA;eAAzBA;;IAtHdC,qBAAqB,EAAA;eAArBA,iCAAAA,qBAAqB;;;uBAnGa;+CAK7B;iDAKA;iCACyB;yBACsB;uCACd;iDAuFjC;AArFP,MAAMC,wBACJ,OAAOC,WAAW,cAEZC,QAAQ,kHACRF,qBAAqB,GACvBG;AAuBC,SAASP;IACd,MAAMQ,eAAeC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,iCAAAA,mBAAmB;IAEnD,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMC,uBAAuBC,CAAAA,GAAAA,OAAAA,OAAO,EAAC;QACnC,IAAI,CAACJ,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIpB,uBAAAA,uBAAuB,CAACoB;IACrC,GAAG;QAACA;KAAa;IAEjB,IAAI,OAAOH,WAAW,aAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEQ,wBAAwB,EAAE,GAChCP,QAAQ;QACV,mEAAmE;QACnEO,yBAAyB;IAC3B;IAEA,OAAOF;AACT;AAoBO,SAASb;IACdM,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,8EAA8E;IAC9E,0EAA0E;IAC1E,OAAOK,CAAAA,GAAAA,OAAAA,UAAU,EAACK,iCAAAA,eAAe;AACnC;AA2BO,SAASf;IACd,MAAMgB,SAASN,CAAAA,GAAAA,OAAAA,UAAU,EAACO,+BAAAA,gBAAgB;IAC1C,IAAID,WAAW,MAAM;QACnB,MAAM,OAAA,cAAwD,CAAxD,IAAIE,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IAEA,OAAOF;AACT;AAoBO,SAASlB;IACdO,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,OAAOK,CAAAA,GAAAA,OAAAA,UAAU,EAACS,iCAAAA,iBAAiB;AACrC;AAEA,0EAA0E,GAC1E,wBAAwB;AACxB,SAASC,6BACPC,IAAuB,EACvBC,gBAAwB,EACxBC,KAAY,EACZC,WAA0B;IAD1BD,IAAAA,UAAAA,KAAAA,GAAAA,QAAQ;IACRC,IAAAA,gBAAAA,KAAAA,GAAAA,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOJ,IAAI,CAAC,EAAE,CAACC,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMI,iBAAiBL,IAAI,CAAC,EAAE;YACvBK;QAAPD,OAAOC,CAAAA,2BAAAA,eAAeC,QAAQ,KAAA,OAAvBD,2BAA2BE,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMM,UAAUL,IAAI,CAAC,EAAE;IAEvB,IAAIM,eAAeC,CAAAA,GAAAA,iBAAAA,eAAe,EAACF;IAEnC,IAAI,CAACC,gBAAgBA,aAAaE,UAAU,CAACC,SAAAA,gBAAgB,GAAG;QAC9D,OAAOV;IACT;IAEAA,YAAYW,IAAI,CAACJ;IAEjB,OAAOX,6BACLK,MACAH,kBACA,OACAE;AAEJ;AA4BO,SAASrB,0BACdmB,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAM+B,UAAU1B,CAAAA,GAAAA,OAAAA,UAAU,EAAC2B,+BAAAA,mBAAmB;IAC9C,wFAAwF;IACxF,IAAI,CAACD,SAAS,OAAO;IAErB,OAAOhB,6BAA6BgB,QAAQE,UAAU,EAAEhB;AAC1D;AAqBO,SAASpB,yBACdoB,gBAAqC;IAArCA,IAAAA,qBAAAA,KAAAA,GAAAA,mBAA2B;IAE3BjB,yBAAAA,OAAAA,KAAAA,IAAAA,sBAAwB;IAExB,MAAMkC,yBAAyBpC,0BAA0BmB;IAEzD,IAAI,CAACiB,0BAA0BA,uBAAuBC,MAAM,KAAK,GAAG;QAClE,OAAO;IACT;IAEA,MAAMC,wBACJnB,qBAAqB,aACjBiB,sBAAsB,CAAC,EAAE,GACzBA,sBAAsB,CAACA,uBAAuBC,MAAM,GAAG,EAAE;IAE/D,yGAAyG;IACzG,yEAAyE;IACzE,OAAOC,0BAA0BC,SAAAA,mBAAmB,GAChD,OACAD;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2945, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/pushGithub/deloyfe/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}