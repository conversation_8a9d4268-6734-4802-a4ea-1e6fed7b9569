// Test email với domain đã cập nhật
const axios = require('axios');

async function testUpdatedEmail() {
  try {
    console.log('🧪 Testing email with UPDATED domain links...');
    const response = await axios.post('http://localhost:3001/api/notifications/email/subscribe-public', {
      email: '<EMAIL>',
      name: 'Test Updated Links',
      taskReminders: true,
      dailySummary: true,
      weeklyReport: false,
      reminderHours: 1
    });
    console.log('✅ Email subscription successful:', response.data);
    console.log('📧 Check your email - links should now go to https://qltime.vercel.app');
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testUpdatedEmail();
