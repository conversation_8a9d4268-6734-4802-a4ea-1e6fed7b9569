// Script để kiểm tra cấu hình backend hiện tại
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function checkBackendConfig() {
  console.log('🔍 Kiểm tra cấu hình backend hiện tại...\n');

  try {
    // Test 1: Kiểm tra backend có chạy không
    console.log('1️⃣ Kiểm tra backend server...');
    const healthResponse = await axios.get(`${API_BASE.replace('/api', '')}/`);
    console.log('✅ Backend server đang chạy');

    // Test 2: Test đăng ký email để xem logs
    console.log('\n2️⃣ Test đăng ký email để xem cấu hình...');
    const testResponse = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>',
      name: 'Config Check Test',
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Test subscription response:', testResponse.data);

    console.log('\n📋 Hãy kiểm tra console của backend server để xem:');
    console.log('   📧 Email Service Configuration:');
    console.log('      EMAIL_USER: <EMAIL>');
    console.log('      FRONTEND_URL: https://qltime.vercel.app (không có dấu / ở cuối)');
    console.log('      Final frontend URL: https://qltime.vercel.app');

    console.log('\n🔧 Nếu vẫn thấy localhost:3000 trong email:');
    console.log('   1. Kiểm tra file .env có FRONTEND_URL=https://qltime.vercel.app');
    console.log('   2. Restart backend server: npm run start:dev');
    console.log('   3. Kiểm tra logs khi backend khởi động');

    console.log('\n💡 Để test email với URL mới:');
    console.log('   node test-email-url-fix.js');

  } catch (error) {
    console.error('❌ Error checking backend config:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
      
      if (error.code === 'ECONNREFUSED') {
        console.log('\n🚨 Backend server không chạy!');
        console.log('   Hãy khởi động backend: cd deloybe && npm run start:dev');
      }
    }
  }
}

// Chạy kiểm tra
checkBackendConfig();
