"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreferencesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const preference_schema_1 = require("./schemas/preference.schema");
let PreferencesService = class PreferencesService {
    preferenceModel;
    constructor(preferenceModel) {
        this.preferenceModel = preferenceModel;
    }
    async getPreferences(userId) {
        let preferences = await this.preferenceModel.findOne({ user: userId }).exec();
        if (!preferences) {
            return this.createDefaultPreferences(userId);
        }
        return preferences;
    }
    async updatePreferences(userId, updatePreferenceDto) {
        const preferences = await this.preferenceModel.findOne({ user: userId }).exec();
        if (!preferences) {
            const defaultPreferences = await this.createDefaultPreferences(userId);
            Object.assign(defaultPreferences, updatePreferenceDto);
            return defaultPreferences.save();
        }
        Object.assign(preferences, updatePreferenceDto);
        return preferences.save();
    }
    async createDefaultPreferences(userId) {
        const defaultPreferences = new this.preferenceModel({
            user: userId,
            theme: 'system',
            language: 'vi',
            notifications: true,
            calendarView: 'week',
            startOfWeek: 1,
            showCompletedTasks: true,
        });
        const savedPreferences = await defaultPreferences.save();
        if (!savedPreferences) {
            throw new common_1.NotFoundException(`Không thể tạo tùy chọn mặc định cho người dùng: ${userId}`);
        }
        return savedPreferences;
    }
};
exports.PreferencesService = PreferencesService;
exports.PreferencesService = PreferencesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(preference_schema_1.Preference.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], PreferencesService);
//# sourceMappingURL=preferences.service.js.map