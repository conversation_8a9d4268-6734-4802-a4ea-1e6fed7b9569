import { CalendarService } from './calendar.service';
import { UserDocument } from '../users/schemas/user.schema';
import { CalendarQueryDto } from './dto/calendar-query.dto';
export declare class CalendarController {
    private readonly calendarService;
    constructor(calendarService: CalendarService);
    getEvents(user: UserDocument, query: CalendarQueryDto): Promise<{
        tasks: import("../tasks/schemas/task.schema").Task[];
        timeBlocks: import("../time-blocks/schemas/time-block.schema").TimeBlock[];
    }>;
    getDayData(user: UserDocument, date: string): Promise<{
        tasks: import("../tasks/schemas/task.schema").Task[];
        timeBlocks: import("../time-blocks/schemas/time-block.schema").TimeBlock[];
    }>;
    getWeekData(user: UserDocument, date: string): Promise<{
        tasks: import("../tasks/schemas/task.schema").Task[];
        timeBlocks: import("../time-blocks/schemas/time-block.schema").TimeBlock[];
    }>;
}
