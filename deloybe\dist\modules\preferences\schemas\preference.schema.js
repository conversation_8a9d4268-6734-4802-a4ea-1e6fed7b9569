"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreferenceSchema = exports.Preference = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const swagger_1 = require("@nestjs/swagger");
let Preference = class Preference {
    user;
    theme;
    language;
    notifications;
    calendarView;
    startOfWeek;
    showCompletedTasks;
};
exports.Preference = Preference;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Người dùng sở hữu',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User', required: true, unique: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Preference.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chủ đề',
        enum: ['light', 'dark', 'system'],
        example: 'system',
    }),
    (0, mongoose_1.Prop)({ enum: ['light', 'dark', 'system'], default: 'system' }),
    __metadata("design:type", String)
], Preference.prototype, "theme", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ngôn ngữ',
        example: 'vi',
    }),
    (0, mongoose_1.Prop)({ default: 'vi' }),
    __metadata("design:type", String)
], Preference.prototype, "language", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bật/tắt thông báo',
        example: true,
    }),
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], Preference.prototype, "notifications", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Chế độ xem lịch mặc định',
        enum: ['day', 'week', 'month'],
        example: 'week',
    }),
    (0, mongoose_1.Prop)({ enum: ['day', 'week', 'month'], default: 'week' }),
    __metadata("design:type", String)
], Preference.prototype, "calendarView", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ngày bắt đầu tuần',
        enum: [0, 1, 6],
        example: 1,
    }),
    (0, mongoose_1.Prop)({ enum: [0, 1, 6], default: 1 }),
    __metadata("design:type", Number)
], Preference.prototype, "startOfWeek", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Hiển thị công việc đã hoàn thành',
        example: true,
    }),
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], Preference.prototype, "showCompletedTasks", void 0);
exports.Preference = Preference = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Preference);
exports.PreferenceSchema = mongoose_1.SchemaFactory.createForClass(Preference);
//# sourceMappingURL=preference.schema.js.map