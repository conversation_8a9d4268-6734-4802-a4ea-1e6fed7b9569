// Test script để kiểm tra email notifications với dữ liệu user thật
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testEmailWithUserData() {
  console.log('🧪 Testing Email Notifications với dữ liệu user thật...\n');

  try {
    // Bước 1: Đăng ký user mới (hoặc đăng nhập)
    console.log('1️⃣ Đăng ký/Đăng nhập user...');
    
    const registerData = {
      name: 'Test User Email',
      email: '<EMAIL>',
      password: 'password123'
    };

    let authToken;
    let userId;

    try {
      // Thử đăng ký user mới
      const registerResponse = await axios.post(`${API_BASE}/auth/register`, registerData);
      console.log('✅ Đăng ký user thành công:', registerResponse.data);
      authToken = registerResponse.data.access_token;
      userId = registerResponse.data.user.id;
    } catch (error) {
      if (error.response?.status === 400) {
        // User đã tồn tại, thử đăng nhập
        console.log('ℹ️ User đã tồn tại, thử đăng nhập...');
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
          email: registerData.email,
          password: registerData.password
        });
        console.log('✅ Đăng nhập thành công:', loginResponse.data);
        authToken = loginResponse.data.access_token;
        userId = loginResponse.data.user.id;
      } else {
        throw error;
      }
    }

    // Bước 2: Tạo một số task test
    console.log('\n2️⃣ Tạo tasks test...');
    
    const headers = { Authorization: `Bearer ${authToken}` };
    
    const tasksToCreate = [
      {
        title: 'Task sắp hết hạn 1',
        description: 'Đây là task test sắp hết hạn',
        dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 giờ nữa
        priority: 'high'
      },
      {
        title: 'Task sắp hết hạn 2', 
        description: 'Task test khác cũng sắp hết hạn',
        dueDate: new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(), // 6 giờ nữa
        priority: 'medium'
      },
      {
        title: 'Task xa trong tương lai',
        description: 'Task này không sắp hết hạn',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 ngày nữa
        priority: 'low'
      }
    ];

    for (const taskData of tasksToCreate) {
      try {
        const taskResponse = await axios.post(`${API_BASE}/tasks`, taskData, { headers });
        console.log(`✅ Tạo task thành công: ${taskResponse.data.title}`);
      } catch (error) {
        console.log(`⚠️ Lỗi tạo task: ${error.response?.data?.message || error.message}`);
      }
    }

    // Bước 3: Đăng ký email notification
    console.log('\n3️⃣ Đăng ký email notification...');
    
    const subscribeData = {
      email: '<EMAIL>', // Email thật để nhận thông báo
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 48 // 48 giờ để bắt được các task test
    };

    const subscribeResponse = await axios.post(
      `${API_BASE}/notifications/email/subscribe`, 
      subscribeData, 
      { headers }
    );
    console.log('✅ Đăng ký email thành công:', subscribeResponse.data);

    // Bước 4: Test gửi email với dữ liệu user
    console.log('\n4️⃣ Test gửi email với dữ liệu user...');
    
    const testResponse = await axios.post(
      `${API_BASE}/notifications/test-email-with-user-data`,
      {},
      { headers }
    );
    
    console.log('✅ Test email response:', JSON.stringify(testResponse.data, null, 2));

    // Bước 5: Kiểm tra subscriptions
    console.log('\n5️⃣ Kiểm tra danh sách subscriptions...');
    
    const subscriptionsResponse = await axios.get(
      `${API_BASE}/notifications/email/subscriptions`,
      { headers }
    );
    console.log('📧 Subscriptions:', subscriptionsResponse.data);

    console.log('\n🎉 Test hoàn thành! Kiểm <NAME_EMAIL> để xem kết quả.');

  } catch (error) {
    console.error('❌ Error testing email notifications:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Chạy test
testEmailWithUserData();
