"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalendarService = void 0;
const common_1 = require("@nestjs/common");
const tasks_service_1 = require("../tasks/tasks.service");
const time_blocks_service_1 = require("../time-blocks/time-blocks.service");
let CalendarService = class CalendarService {
    tasksService;
    timeBlocksService;
    constructor(tasksService, timeBlocksService) {
        this.tasksService = tasksService;
        this.timeBlocksService = timeBlocksService;
    }
    async getCalendarData(userId, start, end) {
        const tasks = await this.tasksService.findAll(userId, {
            dueDate: { $gte: new Date(start), $lte: new Date(end) },
        });
        const timeBlocks = await this.timeBlocksService.findAll(userId);
        const filteredTimeBlocks = timeBlocks.filter(block => {
            const blockDate = new Date(block.startTime);
            return blockDate >= new Date(start) && blockDate <= new Date(end);
        });
        return {
            tasks,
            timeBlocks: filteredTimeBlocks,
        };
    }
    async getDayData(userId, date) {
        const tasks = await this.tasksService.findAll(userId, { dueDate: date });
        const timeBlocks = await this.timeBlocksService.findAll(userId, date);
        return {
            tasks,
            timeBlocks,
        };
    }
    async getWeekData(userId, date) {
        const currentDate = new Date(date);
        const startOfWeek = new Date(currentDate);
        startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
        startOfWeek.setHours(0, 0, 0, 0);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        endOfWeek.setHours(23, 59, 59, 999);
        return this.getCalendarData(userId, startOfWeek.toISOString(), endOfWeek.toISOString());
    }
};
exports.CalendarService = CalendarService;
exports.CalendarService = CalendarService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tasks_service_1.TasksService,
        time_blocks_service_1.TimeBlocksService])
], CalendarService);
//# sourceMappingURL=calendar.service.js.map