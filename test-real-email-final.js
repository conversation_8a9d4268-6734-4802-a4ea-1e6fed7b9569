// Test cuối cùng với email thật
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testRealEmailFinal() {
  console.log('🎯 TEST CUỐI CÙNG - Email thật với debug logs...\n');

  try {
    console.log('📧 Gửi email đến <EMAIL>...');
    console.log('👀 QUAN TRỌNG: Mở console backend và theo dõi!');
    console.log('');

    const timestamp = new Date().toLocaleString('vi-VN');
    
    const response = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>',
      name: `FINAL TEST - ${timestamp}`,
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Email gửi thành công!');
    console.log('Response:', response.data);
    console.log('');

    console.log('🔍 KIỂM TRA NGAY:');
    console.log('');
    console.log('1️⃣ Backend console logs:');
    console.log('   - Có thấy "Final frontend URL: https://qltime.vercel.app"?');
    console.log('   - Có thấy "href=\\"https://qltime.vercel.app\\""?');
    console.log('   - Có thấy "href=\\"https://qltime.vercel.app/register\\""?');
    console.log('');

    console.log('2️⃣ Email <EMAIL>:');
    console.log('   - Mở email mới nhất');
    console.log('   - Subject: "Chào mừng bạn đến với QLTime - Đăng ký thành công!"');
    console.log('   - Name: "FINAL TEST - ' + timestamp + '"');
    console.log('');

    console.log('3️⃣ Click chuột phải vào button "Tạo tài khoản miễn phí":');
    console.log('   - Chọn "Copy link address" hoặc "Sao chép địa chỉ liên kết"');
    console.log('   - Paste vào đây để kiểm tra URL');
    console.log('');

    console.log('🎯 KẾT QUẢ MONG ĐỢI:');
    console.log('   ✅ https://qltime.vercel.app/register');
    console.log('   ❌ KHÔNG PHẢI: http://localhost:3000/register');
    console.log('');

    console.log('📋 Nếu vẫn localhost:');
    console.log('   1. Kiểm tra backend logs có đúng URL không');
    console.log('   2. Restart backend hoàn toàn');
    console.log('   3. Clear cache email client');
    console.log('   4. Thử email client khác');

  } catch (error) {
    console.error('❌ Error sending final test email:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testRealEmailFinal();
