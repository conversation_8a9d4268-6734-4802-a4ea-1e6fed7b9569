import { Document, Types } from 'mongoose';
export type EmailSubscriptionDocument = EmailSubscription & Document;
export declare class EmailSubscription {
    email: string;
    user: Types.ObjectId;
    isActive: boolean;
    taskReminders: boolean;
    dailySummary: boolean;
    weeklyReport: boolean;
    reminderHours: number;
    unsubscribeToken: string;
    lastNotificationSent: Date;
}
export declare const EmailSubscriptionSchema: import("mongoose").Schema<EmailSubscription, import("mongoose").Model<EmailSubscription, any, any, any, Document<unknown, any, EmailSubscription, any> & EmailSubscription & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, EmailSubscription, Document<unknown, {}, import("mongoose").FlatRecord<EmailSubscription>, {}> & import("mongoose").FlatRecord<EmailSubscription> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
