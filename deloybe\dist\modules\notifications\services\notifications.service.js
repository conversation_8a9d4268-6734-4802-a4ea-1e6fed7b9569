"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const email_subscription_schema_1 = require("../schemas/email-subscription.schema");
const public_email_subscription_schema_1 = require("../schemas/public-email-subscription.schema");
const email_service_1 = require("./email.service");
const tasks_service_1 = require("../../tasks/tasks.service");
const users_service_1 = require("../../users/users.service");
const uuid_1 = require("uuid");
let NotificationsService = class NotificationsService {
    emailSubscriptionModel;
    publicEmailSubscriptionModel;
    emailService;
    tasksService;
    usersService;
    constructor(emailSubscriptionModel, publicEmailSubscriptionModel, emailService, tasksService, usersService) {
        this.emailSubscriptionModel = emailSubscriptionModel;
        this.publicEmailSubscriptionModel = publicEmailSubscriptionModel;
        this.emailService = emailService;
        this.tasksService = tasksService;
        this.usersService = usersService;
    }
    async subscribeEmail(userId, subscribeDto) {
        const user = await this.usersService.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('Người dùng không tồn tại');
        }
        const existingSubscription = await this.emailSubscriptionModel.findOne({
            user: new mongoose_2.Types.ObjectId(userId),
            email: subscribeDto.email,
        });
        if (existingSubscription) {
            Object.assign(existingSubscription, {
                ...subscribeDto,
                isActive: true,
                unsubscribeToken: (0, uuid_1.v4)(),
            });
            await existingSubscription.save();
            await this.emailService.sendWelcomeEmail(subscribeDto.email, user.name);
            return existingSubscription;
        }
        const subscription = new this.emailSubscriptionModel({
            ...subscribeDto,
            user: new mongoose_2.Types.ObjectId(userId),
            unsubscribeToken: (0, uuid_1.v4)(),
            taskReminders: subscribeDto.taskReminders ?? true,
            dailySummary: subscribeDto.dailySummary ?? false,
            weeklyReport: subscribeDto.weeklyReport ?? false,
            reminderHours: subscribeDto.reminderHours ?? 24,
        });
        await subscription.save();
        await this.emailService.sendWelcomeEmail(subscribeDto.email, user.name);
        return subscription;
    }
    async subscribeEmailPublic(subscribeDto) {
        const existingSubscription = await this.publicEmailSubscriptionModel.findOne({
            email: subscribeDto.email,
        });
        if (existingSubscription) {
            Object.assign(existingSubscription, {
                ...subscribeDto,
                isActive: true,
                unsubscribeToken: (0, uuid_1.v4)(),
                verificationToken: (0, uuid_1.v4)(),
            });
            await existingSubscription.save();
            await this.emailService.sendWelcomeEmailPublic(subscribeDto.email, subscribeDto.name || 'Bạn', existingSubscription.unsubscribeToken);
            return existingSubscription;
        }
        const subscription = new this.publicEmailSubscriptionModel({
            ...subscribeDto,
            unsubscribeToken: (0, uuid_1.v4)(),
            verificationToken: (0, uuid_1.v4)(),
            taskReminders: subscribeDto.taskReminders ?? true,
            dailySummary: subscribeDto.dailySummary ?? false,
            weeklyReport: subscribeDto.weeklyReport ?? false,
            reminderHours: subscribeDto.reminderHours ?? 24,
            emailVerified: false,
        });
        await subscription.save();
        await this.emailService.sendWelcomeEmailPublic(subscribeDto.email, subscribeDto.name || 'Bạn', subscription.unsubscribeToken);
        return subscription;
    }
    async getSubscription(userId) {
        return this.emailSubscriptionModel.find({
            user: new mongoose_2.Types.ObjectId(userId),
        }).exec();
    }
    async updateSubscription(userId, subscriptionId, updateDto) {
        const subscription = await this.emailSubscriptionModel.findOne({
            _id: new mongoose_2.Types.ObjectId(subscriptionId),
            user: new mongoose_2.Types.ObjectId(userId),
        });
        if (!subscription) {
            throw new common_1.NotFoundException('Đăng ký email không tồn tại');
        }
        Object.assign(subscription, updateDto);
        await subscription.save();
        return subscription;
    }
    async unsubscribe(token) {
        const subscription = await this.emailSubscriptionModel.findOne({
            unsubscribeToken: token,
        });
        if (!subscription) {
            throw new common_1.NotFoundException('Token hủy đăng ký không hợp lệ');
        }
        subscription.isActive = false;
        await subscription.save();
        return { message: 'Đã hủy đăng ký thành công' };
    }
    async sendTaskReminders() {
        let sent = 0;
        let failed = 0;
        const subscriptions = await this.emailSubscriptionModel.find({
            isActive: true,
            taskReminders: true,
        }).populate('user');
        for (const subscription of subscriptions) {
            try {
                const reminderTime = new Date();
                reminderTime.setHours(reminderTime.getHours() + subscription.reminderHours);
                const tasks = await this.tasksService.findTasksDueSoon(subscription.user.toString(), subscription.reminderHours);
                if (tasks.length > 0) {
                    const lastSent = subscription.lastNotificationSent;
                    const twelveHoursAgo = new Date();
                    twelveHoursAgo.setHours(twelveHoursAgo.getHours() - 12);
                    if (!lastSent || lastSent < twelveHoursAgo) {
                        const success = await this.emailService.sendTaskReminderEmail(subscription.email, tasks);
                        if (success) {
                            subscription.lastNotificationSent = new Date();
                            await subscription.save();
                            sent++;
                        }
                        else {
                            failed++;
                        }
                    }
                }
            }
            catch (error) {
                console.error(`Failed to send reminder to ${subscription.email}:`, error);
                failed++;
            }
        }
        return { sent, failed };
    }
    async deleteSubscription(userId, subscriptionId) {
        const result = await this.emailSubscriptionModel.deleteOne({
            _id: new mongoose_2.Types.ObjectId(subscriptionId),
            user: new mongoose_2.Types.ObjectId(userId),
        });
        if (result.deletedCount === 0) {
            throw new common_1.NotFoundException('Đăng ký email không tồn tại');
        }
        return { message: 'Đã xóa đăng ký email thành công' };
    }
    async testSendEmailWithUserData(userId) {
        try {
            console.log(`🧪 Testing email notification for user: ${userId}`);
            const user = await this.usersService.findById(userId);
            if (!user) {
                throw new common_1.NotFoundException('User không tồn tại');
            }
            console.log(`✅ Found user: ${user.name} (${user.email})`);
            const subscriptions = await this.emailSubscriptionModel.find({
                user: new mongoose_2.Types.ObjectId(userId),
                isActive: true,
                taskReminders: true,
            });
            console.log(`📧 Found ${subscriptions.length} active email subscriptions`);
            if (subscriptions.length === 0) {
                return {
                    success: false,
                    message: 'Không có email subscription nào được tìm thấy cho user này',
                    userInfo: { id: userId, name: user.name, email: user.email }
                };
            }
            const tasks = await this.tasksService.findTasksDueSoon(userId, 48);
            console.log(`📋 Found ${tasks.length} tasks due soon`);
            const results = [];
            for (const subscription of subscriptions) {
                console.log(`📤 Sending email to: ${subscription.email}`);
                if (tasks.length > 0) {
                    const success = await this.emailService.sendTaskReminderEmail(subscription.email, tasks);
                    results.push({
                        email: subscription.email,
                        success,
                        tasksCount: tasks.length,
                        tasks: tasks.map(t => ({ title: t.title, dueDate: t.dueDate }))
                    });
                    if (success) {
                        subscription.lastNotificationSent = new Date();
                        await subscription.save();
                    }
                }
                else {
                    const success = await this.emailService.sendEmail({
                        to: subscription.email,
                        subject: 'QLTime - Test Email (Không có task sắp hết hạn)',
                        html: `
              <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #2563eb;">🧪 Test Email từ QLTime</h2>
                <p>Xin chào <strong>${user.name}</strong>,</p>
                <p>Đây là email test từ hệ thống QLTime.</p>
                <p><strong>Thông tin:</strong></p>
                <ul>
                  <li>User ID: ${userId}</li>
                  <li>Email đăng ký: ${subscription.email}</li>
                  <li>Số task sắp hết hạn: ${tasks.length}</li>
                </ul>
                <p>Hệ thống email đang hoạt động bình thường! ✅</p>
              </div>
            `,
                        text: `Test email từ QLTime cho ${user.name}. Hệ thống hoạt động bình thường.`
                    });
                    results.push({
                        email: subscription.email,
                        success,
                        tasksCount: 0,
                        message: 'Test email sent (no tasks due)'
                    });
                }
            }
            return {
                success: true,
                userInfo: { id: userId, name: user.name, email: user.email },
                subscriptions: subscriptions.length,
                tasksFound: tasks.length,
                results
            };
        }
        catch (error) {
            console.error('❌ Error in testSendEmailWithUserData:', error);
            return {
                success: false,
                error: error.message,
                userInfo: { id: userId }
            };
        }
    }
};
exports.NotificationsService = NotificationsService;
exports.NotificationsService = NotificationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(email_subscription_schema_1.EmailSubscription.name)),
    __param(1, (0, mongoose_1.InjectModel)(public_email_subscription_schema_1.PublicEmailSubscription.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        email_service_1.EmailService,
        tasks_service_1.TasksService,
        users_service_1.UsersService])
], NotificationsService);
//# sourceMappingURL=notifications.service.js.map