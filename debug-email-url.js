// Script để debug URL trong email
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function debugEmailUrl() {
  console.log('🔍 Debug Email URL Issue...\n');

  try {
    console.log('1️⃣ Kiểm tra backend có chạy không...');
    await axios.get(`${API_BASE.replace('/api', '')}/`);
    console.log('✅ Backend đang chạy');

    console.log('\n2️⃣ Test đăng ký email và kiểm tra response...');
    
    // Tạo email test với timestamp để tránh duplicate
    const testEmail = `debug-${Date.now()}@example.com`;
    
    const response = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: testEmail,
      name: 'Debug URL Test',
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Subscription response:', response.data);

    console.log('\n3️⃣ Bây giờ hãy kiểm tra console của backend server...');
    console.log('Tìm các dòng log sau:');
    console.log('📧 Email Service Configuration:');
    console.log('   EMAIL_USER: <EMAIL>');
    console.log('   FRONTEND_URL: https://qltime.vercel.app');
    console.log('   Final frontend URL: https://qltime.vercel.app');

    console.log('\n4️⃣ Nếu vẫn thấy localhost trong email:');
    console.log('🔄 RESTART BACKEND SERVER:');
    console.log('   1. Dừng backend (Ctrl+C)');
    console.log('   2. cd deloybe');
    console.log('   3. npm run start:dev');
    console.log('   4. Chờ thấy log "Email Service Configuration"');
    console.log('   5. Chạy lại script này');

    console.log('\n5️⃣ Kiểm tra email production:');
    console.log('Nếu backend đã restart, test với email thật:');
    console.log('node test-production-email.js');

  } catch (error) {
    console.error('❌ Error:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
      
      if (error.code === 'ECONNREFUSED') {
        console.log('\n🚨 Backend không chạy!');
        console.log('Khởi động backend:');
        console.log('cd deloybe && npm run start:dev');
      }
    }
  }
}

debugEmailUrl();
