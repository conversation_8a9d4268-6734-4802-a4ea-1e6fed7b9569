"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SchedulerService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulerService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const public_email_subscription_schema_1 = require("../schemas/public-email-subscription.schema");
const email_service_1 = require("./email.service");
const tasks_service_1 = require("../../tasks/tasks.service");
const users_service_1 = require("../../users/users.service");
let SchedulerService = SchedulerService_1 = class SchedulerService {
    publicEmailSubscriptionModel;
    emailService;
    tasksService;
    usersService;
    logger = new common_1.Logger(SchedulerService_1.name);
    constructor(publicEmailSubscriptionModel, emailService, tasksService, usersService) {
        this.publicEmailSubscriptionModel = publicEmailSubscriptionModel;
        this.emailService = emailService;
        this.tasksService = tasksService;
        this.usersService = usersService;
    }
    async sendDailyTaskReminders() {
        this.logger.log('Bắt đầu gửi thông báo nhắc nhở hàng ngày...');
        try {
            const publicResult = await this.sendPublicTaskReminders();
            const userResult = await this.sendUserTaskReminders();
            this.logger.log(`Hoàn thành gửi thông báo: Public(${publicResult.sent}/${publicResult.total}), User(${userResult.sent}/${userResult.failed})`);
        }
        catch (error) {
            this.logger.error('Lỗi khi gửi thông báo hàng ngày:', error);
        }
    }
    async sendUrgentTaskReminders() {
        this.logger.log('Kiểm tra tasks cần thông báo khẩn cấp...');
        try {
            const publicResult = await this.sendPublicTaskReminders(4);
            if (publicResult.sent > 0) {
                this.logger.log(`Đã gửi ${publicResult.sent} thông báo khẩn cấp`);
            }
        }
        catch (error) {
            this.logger.error('Lỗi khi gửi thông báo khẩn cấp:', error);
        }
    }
    async sendPublicTaskReminders(urgentHours) {
        let sent = 0;
        let failed = 0;
        const subscriptions = await this.publicEmailSubscriptionModel.find({
            isActive: true,
            taskReminders: true,
        });
        this.logger.log(`Tìm thấy ${subscriptions.length} public email subscriptions`);
        for (const subscription of subscriptions) {
            try {
                const user = await this.usersService.findByEmail(subscription.email);
                if (!user) {
                    this.logger.debug(`Không tìm thấy user với email: ${subscription.email}`);
                    continue;
                }
                const hoursToCheck = urgentHours || subscription.reminderHours;
                const tasks = await this.tasksService.findTasksDueSoon(user._id.toString(), hoursToCheck);
                if (tasks.length > 0) {
                    const shouldSend = this.shouldSendReminder(subscription, urgentHours);
                    if (shouldSend) {
                        const success = await this.emailService.sendTaskReminderEmailPublic(subscription.email, subscription.name || 'Bạn', tasks, subscription.unsubscribeToken);
                        if (success) {
                            subscription.lastNotificationSent = new Date();
                            await subscription.save();
                            sent++;
                            this.logger.log(`Đã gửi thông báo cho ${subscription.email}: ${tasks.length} tasks`);
                        }
                        else {
                            failed++;
                            this.logger.error(`Gửi thông báo thất bại cho ${subscription.email}`);
                        }
                    }
                    else {
                        this.logger.debug(`Bỏ qua ${subscription.email} - đã gửi thông báo gần đây`);
                    }
                }
            }
            catch (error) {
                this.logger.error(`Lỗi khi xử lý subscription ${subscription.email}:`, error);
                failed++;
            }
        }
        return { sent, failed, total: subscriptions.length };
    }
    async sendUserTaskReminders() {
        return { sent: 0, failed: 0 };
    }
    shouldSendReminder(subscription, urgentHours) {
        if (!subscription.lastNotificationSent) {
            return true;
        }
        const now = new Date();
        const lastSent = new Date(subscription.lastNotificationSent);
        const hoursSinceLastSent = (now.getTime() - lastSent.getTime()) / (1000 * 60 * 60);
        if (urgentHours) {
            return hoursSinceLastSent >= 2;
        }
        else {
            return hoursSinceLastSent >= 12;
        }
    }
    async testSendReminders() {
        this.logger.log('Test gửi thông báo thủ công...');
        return this.sendPublicTaskReminders();
    }
};
exports.SchedulerService = SchedulerService;
__decorate([
    (0, schedule_1.Cron)('0 8 * * *', {
        name: 'daily-task-reminders',
        timeZone: 'Asia/Ho_Chi_Minh',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SchedulerService.prototype, "sendDailyTaskReminders", null);
__decorate([
    (0, schedule_1.Cron)('0 */2 * * *', {
        name: 'urgent-task-reminders',
        timeZone: 'Asia/Ho_Chi_Minh',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SchedulerService.prototype, "sendUrgentTaskReminders", null);
exports.SchedulerService = SchedulerService = SchedulerService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(public_email_subscription_schema_1.PublicEmailSubscription.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        email_service_1.EmailService,
        tasks_service_1.TasksService,
        users_service_1.UsersService])
], SchedulerService);
//# sourceMappingURL=scheduler.service.js.map