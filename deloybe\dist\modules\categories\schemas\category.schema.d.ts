import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class Category extends Document {
    name: string;
    color: string;
    user: MongooseSchema.Types.ObjectId;
}
export declare const CategorySchema: MongooseSchema<Category, import("mongoose").Model<Category, any, any, any, Document<unknown, any, Category, any> & Category & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Category, Document<unknown, {}, import("mongoose").FlatRecord<Category>, {}> & import("mongoose").FlatRecord<Category> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
