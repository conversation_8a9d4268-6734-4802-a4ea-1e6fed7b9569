// Test script hoàn chỉnh để kiểm tra toàn bộ hệ thống email
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testCompleteEmailSystem() {
  console.log('🧪 Testing Complete Email System...\n');

  try {
    // Test 1: Kiểm tra backend server
    console.log('1️⃣ Kiểm tra backend server...');
    try {
      await axios.get(`${API_BASE.replace('/api', '')}/`);
      console.log('✅ Backend server đang chạy');
    } catch (error) {
      console.log('❌ Backend server không chạy!');
      console.log('   Hãy khởi động: cd deloybe && npm run start:dev');
      return;
    }

    // Test 2: Đăng ký email public
    console.log('\n2️⃣ Test đăng ký email public...');
    const publicResponse = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>',
      name: 'Complete System Test',
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 24
    });
    
    console.log('✅ Public subscription successful:', publicResponse.data);

    // Test 3: Đăng ký user và tạo tasks
    console.log('\n3️⃣ Đăng ký user và tạo tasks...');
    
    const userData = {
      name: 'Test User Complete',
      email: '<EMAIL>',
      password: 'password123'
    };

    let authToken;
    let userId;

    try {
      const registerResponse = await axios.post(`${API_BASE}/auth/register`, userData);
      authToken = registerResponse.data.access_token;
      userId = registerResponse.data.user.id;
      console.log('✅ User registered successfully');
    } catch (error) {
      if (error.response?.status === 400) {
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
          email: userData.email,
          password: userData.password
        });
        authToken = loginResponse.data.access_token;
        userId = loginResponse.data.user.id;
        console.log('✅ User logged in successfully');
      } else {
        throw error;
      }
    }

    // Tạo tasks test
    const headers = { Authorization: `Bearer ${authToken}` };
    
    const taskData = {
      title: 'Task test hoàn chỉnh',
      description: 'Task để test hệ thống email hoàn chỉnh',
      dueDate: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(), // 4 giờ nữa
      priority: 'high'
    };

    const taskResponse = await axios.post(`${API_BASE}/tasks`, taskData, { headers });
    console.log('✅ Task created:', taskResponse.data.title);

    // Test 4: Đăng ký email cho user
    console.log('\n4️⃣ Đăng ký email cho user...');
    
    const subscribeData = {
      email: '<EMAIL>',
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 48
    };

    const subscribeResponse = await axios.post(
      `${API_BASE}/notifications/email/subscribe`, 
      subscribeData, 
      { headers }
    );
    console.log('✅ User email subscription successful');

    // Test 5: Test gửi email với dữ liệu user
    console.log('\n5️⃣ Test gửi email với dữ liệu user...');
    
    const testEmailResponse = await axios.post(
      `${API_BASE}/notifications/test-email-with-user-data`,
      {},
      { headers }
    );
    
    console.log('✅ Test email response:', JSON.stringify(testEmailResponse.data, null, 2));

    // Test 6: Test scheduler
    console.log('\n6️⃣ Test scheduler...');
    
    const schedulerResponse = await axios.post(`${API_BASE}/notifications/test-scheduler`);
    console.log('✅ Scheduler test:', schedulerResponse.data);

    // Kết quả
    console.log('\n🎉 Test hoàn chỉnh thành công!');
    console.log('\n📧 Kiểm <NAME_EMAIL> để xem:');
    console.log('   1. Email chào mừng từ public subscription');
    console.log('   2. Email chào mừng từ user subscription');
    console.log('   3. Email nhắc nhở task (nếu có)');
    console.log('\n🔗 Tất cả links trong email phải dẫn đến:');
    console.log('   - https://qltime.vercel.app');
    console.log('   - https://qltime.vercel.app/register');
    console.log('   - https://qltime.vercel.app/login');
    console.log('   - https://qltime.vercel.app/tasks');

    console.log('\n📋 Thông tin test:');
    console.log(`   User ID: ${userId}`);
    console.log(`   Email: ${subscribeData.email}`);
    console.log(`   Tasks created: 1`);

  } catch (error) {
    console.error('❌ Error in complete email system test:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Chạy test
testCompleteEmailSystem();
