"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateNoteDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class CreateNoteDto {
    title;
    content;
    tags;
    color;
}
exports.CreateNoteDto = CreateNoteDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề ghi chú',
        example: 'Ý tưởng dự án mới',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Tiêu đề không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề phải là chuỗi' }),
    __metadata("design:type", String)
], CreateNoteDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nội dung ghi chú',
        example: 'Chi tiết về ý tưởng dự án quản lý thời gian...',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nội dung không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Nội dung phải là chuỗi' }),
    __metadata("design:type", String)
], CreateNoteDto.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Danh sách thẻ',
        example: ['ý tưởng', 'dự án'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Tags phải là mảng' }),
    (0, class_validator_1.IsString)({ each: true, message: 'Mỗi thẻ phải là chuỗi' }),
    __metadata("design:type", Array)
], CreateNoteDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mã màu',
        example: '#4CAF50',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsHexColor)({ message: 'Mã màu phải là mã hex hợp lệ' }),
    __metadata("design:type", String)
], CreateNoteDto.prototype, "color", void 0);
//# sourceMappingURL=create-note.dto.js.map