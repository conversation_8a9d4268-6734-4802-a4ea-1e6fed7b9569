(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/_9e817198._.js", {

"[project]/src/lib/services/api-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiService": (()=>ApiService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
// URL cơ sở cho API
const API_BASE_URL = ("TURBOPACK compile-time value", "http://localhost:3001/api") || 'https://qltimebe.onrender.com/api';
// Hàm trợ giúp để xử lý lỗi từ phản hồi fetch
const handleResponse = async (response)=>{
    if (!response.ok) {
        console.log(`handleResponse: Response not OK - Status: ${response.status}, StatusText: ${response.statusText}`);
        // Xử lý lỗi 401 (Unauthorized) - token hết hạn hoặc không hợp lệ
        if (response.status === 401) {
            console.log('handleResponse: 401 Unauthorized - Xóa token');
            // Xóa token không hợp lệ
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.removeItem('authToken');
            }
        }
        const errorData = await response.json().catch(()=>({}));
        console.log('handleResponse: Error data:', errorData);
        throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);
    }
    // Kiểm tra nếu response có content
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
        const text = await response.text();
        if (text && text !== 'undefined' && text !== 'null' && text.trim() !== '') {
            try {
                return JSON.parse(text);
            } catch (parseError) {
                console.error('JSON parse error:', parseError, 'Text:', text);
                throw new Error('Phản hồi từ server không hợp lệ');
            }
        }
        return null;
    }
    // Nếu không có content-type JSON, trả về null cho DELETE requests
    return null;
};
// Hàm wrapper để xử lý lỗi kết nối
const handleFetchError = async (fetchPromise)=>{
    try {
        return await fetchPromise;
    } catch (error) {
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
            throw new Error('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng hoặc đảm bảo server đang chạy.');
        }
        throw error;
    }
};
// Hàm xử lý response đặc biệt cho getById - không throw error cho 404
const handleResponseForGetById = async (response)=>{
    if (!response.ok) {
        if (response.status === 404) {
            // Trả về null thay vì throw error cho 404
            return null;
        }
        const errorData = await response.json().catch(()=>({}));
        throw new Error(errorData.message || `Lỗi ${response.status}: ${response.statusText}`);
    }
    // Kiểm tra nếu response có content
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
        const text = await response.text();
        if (text && text !== 'undefined' && text !== 'null' && text.trim() !== '') {
            try {
                return JSON.parse(text);
            } catch (parseError) {
                console.error('JSON parse error:', parseError, 'Text:', text);
                throw new Error('Phản hồi từ server không hợp lệ');
            }
        }
        return null;
    }
    // Nếu không có content-type JSON, trả về null cho DELETE requests
    return null;
};
// Lấy token xác thực từ localStorage
const getAuthToken = ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        return localStorage.getItem('authToken');
    }
    "TURBOPACK unreachable";
};
// Tạo headers cơ bản cho các yêu cầu API
const getHeaders = ()=>{
    const headers = {
        'Content-Type': 'application/json'
    };
    const token = getAuthToken();
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    return headers;
};
// Tạo headers cho DELETE requests (không có Content-Type)
const getDeleteHeaders = ()=>{
    const headers = {};
    const token = getAuthToken();
    if (token) {
        headers['Authorization'] = `Bearer ${token}`;
    }
    return headers;
};
const ApiService = {
    // AUTH ENDPOINTS
    auth: {
        login: async (email, password)=>{
            const response = await handleFetchError(fetch(`${API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify({
                    email,
                    password
                })
            }));
            const data = await handleResponse(response);
            if (data.token) {
                localStorage.setItem('authToken', data.token);
            }
            return data;
        },
        register: async (name, email, password)=>{
            const response = await fetch(`${API_BASE_URL}/auth/register`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify({
                    name,
                    email,
                    password
                })
            });
            return handleResponse(response);
        },
        logout: ()=>{
            localStorage.removeItem('authToken');
        },
        getCurrentUser: async ()=>{
            console.log('ApiService.getCurrentUser: Đang gọi API /auth/me...');
            const headers = getHeaders();
            console.log('ApiService.getCurrentUser: Headers:', headers);
            const response = await handleFetchError(fetch(`${API_BASE_URL}/auth/me`, {
                headers: headers
            }));
            console.log('ApiService.getCurrentUser: Response status:', response.status);
            const result = await handleResponse(response);
            console.log('ApiService.getCurrentUser: Result:', result);
            return result;
        }
    },
    // TASK ENDPOINTS
    tasks: {
        getAll: async ()=>{
            const response = await fetch(`${API_BASE_URL}/tasks`, {
                headers: getHeaders()
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return data.map((task)=>({
                    ...task,
                    id: task._id || task.id
                }));
        },
        getById: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {
                headers: getHeaders()
            });
            return handleResponseForGetById(response);
        },
        create: async (task)=>{
            const response = await fetch(`${API_BASE_URL}/tasks`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify(task)
            });
            return handleResponse(response);
        },
        update: async (id, updates)=>{
            const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {
                method: 'PUT',
                headers: getHeaders(),
                body: JSON.stringify(updates)
            });
            return handleResponse(response);
        },
        delete: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/tasks/${id}`, {
                method: 'DELETE',
                headers: getDeleteHeaders()
            });
            return handleResponse(response);
        },
        toggleCompletion: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/tasks/${id}/complete`, {
                method: 'PATCH',
                headers: getDeleteHeaders()
            });
            return handleResponse(response);
        }
    },
    // CATEGORY ENDPOINTS
    categories: {
        getAll: async ()=>{
            const response = await fetch(`${API_BASE_URL}/categories`, {
                headers: getHeaders()
            });
            return handleResponse(response);
        },
        getById: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
                headers: getHeaders()
            });
            return handleResponse(response);
        },
        create: async (category)=>{
            const response = await fetch(`${API_BASE_URL}/categories`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify(category)
            });
            return handleResponse(response);
        },
        update: async (id, updates)=>{
            const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
                method: 'PUT',
                headers: getHeaders(),
                body: JSON.stringify(updates)
            });
            return handleResponse(response);
        },
        delete: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/categories/${id}`, {
                method: 'DELETE',
                headers: getDeleteHeaders()
            });
            return handleResponse(response);
        }
    },
    // TIME BLOCK ENDPOINTS
    timeBlocks: {
        getAll: async ()=>{
            const response = await fetch(`${API_BASE_URL}/time-blocks`, {
                headers: getHeaders()
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return data.map((timeBlock)=>({
                    ...timeBlock,
                    id: timeBlock._id || timeBlock.id
                }));
        },
        getByDate: async (date)=>{
            const response = await fetch(`${API_BASE_URL}/time-blocks/date/${date}`, {
                headers: getHeaders()
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return data.map((timeBlock)=>({
                    ...timeBlock,
                    id: timeBlock._id || timeBlock.id
                }));
        },
        getById: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {
                headers: getHeaders()
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return {
                ...data,
                id: data._id || data.id
            };
        },
        create: async (timeBlock)=>{
            const response = await fetch(`${API_BASE_URL}/time-blocks`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify(timeBlock)
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return {
                ...data,
                id: data._id || data.id
            };
        },
        update: async (id, updates)=>{
            console.log('ApiService: Updating timeBlock with id:', id, 'updates:', updates);
            const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {
                method: 'PUT',
                headers: getHeaders(),
                body: JSON.stringify(updates)
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return {
                ...data,
                id: data._id || data.id
            };
        },
        delete: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {
                method: 'DELETE',
                headers: getDeleteHeaders()
            });
            return handleResponse(response);
        },
        toggleCompletion: async (id, isCompleted)=>{
            console.log('ApiService: Toggling timeBlock completion:', id, isCompleted);
            const response = await fetch(`${API_BASE_URL}/time-blocks/${id}`, {
                method: 'PUT',
                headers: getHeaders(),
                body: JSON.stringify({
                    isCompleted
                })
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return {
                ...data,
                id: data._id || data.id
            };
        }
    },
    // PREFERENCE ENDPOINTS
    preferences: {
        get: async ()=>{
            const response = await fetch(`${API_BASE_URL}/preferences`, {
                headers: getHeaders()
            });
            return handleResponse(response);
        },
        update: async (updates)=>{
            const response = await fetch(`${API_BASE_URL}/preferences`, {
                method: 'PATCH',
                headers: getHeaders(),
                body: JSON.stringify(updates)
            });
            return handleResponse(response);
        }
    },
    // PROJECT ENDPOINTS
    projects: {
        getAll: async ()=>{
            const response = await fetch(`${API_BASE_URL}/projects`, {
                headers: getHeaders()
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return data.map((project)=>({
                    ...project,
                    id: project._id || project.id
                }));
        },
        getById: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/projects/${id}`, {
                headers: getHeaders()
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return {
                ...data,
                id: data._id || data.id
            };
        },
        create: async (project)=>{
            const response = await fetch(`${API_BASE_URL}/projects`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify(project)
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return {
                ...data,
                id: data._id || data.id
            };
        },
        update: async (id, updates)=>{
            const response = await fetch(`${API_BASE_URL}/projects/${id}`, {
                method: 'PUT',
                headers: getHeaders(),
                body: JSON.stringify(updates)
            });
            const data = await handleResponse(response);
            // Map _id to id for frontend compatibility
            return {
                ...data,
                id: data._id || data.id
            };
        },
        delete: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/projects/${id}`, {
                method: 'DELETE',
                headers: getDeleteHeaders()
            });
            return handleResponse(response);
        }
    },
    // NOTE ENDPOINTS
    notes: {
        getAll: async ()=>{
            const response = await fetch(`${API_BASE_URL}/notes`, {
                headers: getHeaders()
            });
            return handleResponse(response);
        },
        getById: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/notes/${id}`, {
                headers: getHeaders()
            });
            return handleResponse(response);
        },
        create: async (note)=>{
            const response = await fetch(`${API_BASE_URL}/notes`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify(note)
            });
            return handleResponse(response);
        },
        update: async (id, updates)=>{
            const response = await fetch(`${API_BASE_URL}/notes/${id}`, {
                method: 'PATCH',
                headers: getHeaders(),
                body: JSON.stringify(updates)
            });
            return handleResponse(response);
        },
        delete: async (id)=>{
            const response = await fetch(`${API_BASE_URL}/notes/${id}`, {
                method: 'DELETE',
                headers: getDeleteHeaders()
            });
            return handleResponse(response);
        }
    },
    // STATISTICS ENDPOINTS
    statistics: {
        getTaskStats: async (startDate, endDate)=>{
            const params = new URLSearchParams();
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);
            const url = `${API_BASE_URL}/statistics/tasks${params.toString() ? `?${params.toString()}` : ''}`;
            const response = await fetch(url, {
                headers: getHeaders()
            });
            return handleResponse(response);
        },
        getTimeBlockStats: async (startDate, endDate)=>{
            const params = new URLSearchParams();
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);
            const url = `${API_BASE_URL}/statistics/time-blocks${params.toString() ? `?${params.toString()}` : ''}`;
            const response = await fetch(url, {
                headers: getHeaders()
            });
            return handleResponse(response);
        },
        getProductivityStats: async (startDate, endDate)=>{
            const params = new URLSearchParams();
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);
            const url = `${API_BASE_URL}/statistics/productivity${params.toString() ? `?${params.toString()}` : ''}`;
            const response = await fetch(url, {
                headers: getHeaders()
            });
            return handleResponse(response);
        }
    },
    // USER ENDPOINTS
    users: {
        getProfile: async ()=>{
            const response = await fetch(`${API_BASE_URL}/users/profile`, {
                headers: getHeaders()
            });
            return handleResponse(response);
        },
        updateProfile: async (updates)=>{
            const response = await fetch(`${API_BASE_URL}/users/profile`, {
                method: 'PUT',
                headers: getHeaders(),
                body: JSON.stringify(updates)
            });
            return handleResponse(response);
        }
    },
    // NOTIFICATIONS ENDPOINTS
    notifications: {
        subscribeEmail: async (subscriptionData)=>{
            const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscribe`, {
                method: 'POST',
                headers: getHeaders(),
                body: JSON.stringify(subscriptionData)
            }));
            return handleResponse(response);
        },
        subscribeEmailPublic: async (subscriptionData)=>{
            const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscribe-public`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(subscriptionData)
            }));
            return handleResponse(response);
        },
        getSubscriptions: async ()=>{
            const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions`, {
                headers: getHeaders()
            }));
            return handleResponse(response);
        },
        updateSubscription: async (id, updateData)=>{
            const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions/${id}`, {
                method: 'PUT',
                headers: getHeaders(),
                body: JSON.stringify(updateData)
            }));
            return handleResponse(response);
        },
        deleteSubscription: async (id)=>{
            const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/subscriptions/${id}`, {
                method: 'DELETE',
                headers: getHeaders()
            }));
            return handleResponse(response);
        },
        unsubscribe: async (token)=>{
            const response = await handleFetchError(fetch(`${API_BASE_URL}/notifications/email/unsubscribe?token=${token}`, {
                method: 'POST'
            }));
            return handleResponse(response);
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/auth/auth-guard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthGuard": (()=>AuthGuard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/api-service.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function AuthGuard({ children }) {
    _s();
    const [isAuthenticated, setIsAuthenticated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthGuard.useEffect": ()=>{
            const checkAuth = {
                "AuthGuard.useEffect.checkAuth": async ()=>{
                    console.log('AuthGuard: Bắt đầu kiểm tra authentication...');
                    const token = localStorage.getItem('authToken');
                    console.log('AuthGuard: Token từ localStorage:', token ? `${token.substring(0, 20)}...` : 'null');
                    if (!token) {
                        console.log('AuthGuard: Không có token, chuyển hướng đến login');
                        setIsAuthenticated(false);
                        router.push('/login');
                        return;
                    }
                    try {
                        console.log('AuthGuard: Đang kiểm tra token với API...');
                        // Kiểm tra token có hợp lệ không
                        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$api$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ApiService"].auth.getCurrentUser();
                        console.log('AuthGuard: Token hợp lệ, user:', user);
                        setIsAuthenticated(true);
                    } catch (error) {
                        console.error('AuthGuard: Token không hợp lệ:', error);
                        localStorage.removeItem('authToken');
                        setIsAuthenticated(false);
                        router.push('/login');
                    }
                }
            }["AuthGuard.useEffect.checkAuth"];
            checkAuth();
        }
    }["AuthGuard.useEffect"], [
        router
    ]);
    // Đang kiểm tra authentication
    if (isAuthenticated === null) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/auth-guard.tsx",
                        lineNumber: 50,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-2 text-muted-foreground",
                        children: "Đang kiểm tra đăng nhập..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/auth/auth-guard.tsx",
                        lineNumber: 51,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/auth/auth-guard.tsx",
                lineNumber: 49,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/auth/auth-guard.tsx",
            lineNumber: 48,
            columnNumber: 7
        }, this);
    }
    // Chưa đăng nhập
    if (!isAuthenticated) {
        return null; // Router sẽ chuyển hướng
    }
    // Đã đăng nhập
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AuthGuard, "nzonxlEY4BnQFLufoeYb1M3pCFc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = AuthGuard;
var _c;
__turbopack_context__.k.register(_c, "AuthGuard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/auth/auth-wrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthWrapper": (()=>AuthWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$auth$2d$guard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/auth-guard.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
// Các trang không cần authentication - có thể truy cập tự do
const publicRoutes = [
    '/login',
    '/register',
    '/',
    '/tasks',
    '/projects',
    '/calendar',
    '/statistics',
    '/categories',
    '/notes'
];
// Các trang cần authentication bắt buộc
const protectedRoutes = [
    '/profile',
    '/settings'
];
function AuthWrapper({ children }) {
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    // Nếu là trang public, không cần AuthGuard
    if (publicRoutes.includes(pathname)) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: children
        }, void 0, false);
    }
    // Nếu là trang protected, bắt buộc phải có AuthGuard
    if (protectedRoutes.some((route)=>pathname.startsWith(route))) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$auth$2d$guard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthGuard"], {
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/auth/auth-wrapper.tsx",
            lineNumber: 40,
            columnNumber: 7
        }, this);
    }
    // Các trang khác cũng có thể truy cập tự do (fallback)
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(AuthWrapper, "xbyQPtUVMO7MNj7WjJlpdWqRcTo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = AuthWrapper;
var _c;
__turbopack_context__.k.register(_c, "AuthWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/contexts/statistics-context.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "StatisticsProvider": (()=>StatisticsProvider),
    "useStatistics": (()=>useStatistics),
    "useStatisticsRefresh": (()=>useStatisticsRefresh)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
const StatisticsContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function StatisticsProvider({ children }) {
    _s();
    const [refreshTrigger, setRefreshTrigger] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const triggerRefresh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StatisticsProvider.useCallback[triggerRefresh]": ()=>{
            console.log('StatisticsContext: Triggering statistics refresh');
            setRefreshTrigger({
                "StatisticsProvider.useCallback[triggerRefresh]": (prev)=>prev + 1
            }["StatisticsProvider.useCallback[triggerRefresh]"]);
        }
    }["StatisticsProvider.useCallback[triggerRefresh]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatisticsContext.Provider, {
        value: {
            refreshTrigger,
            triggerRefresh
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/lib/contexts/statistics-context.tsx",
        lineNumber: 21,
        columnNumber: 5
    }, this);
}
_s(StatisticsProvider, "XIU9cGhiOhnZxAzCnxtIX6m3yd0=");
_c = StatisticsProvider;
function useStatistics() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(StatisticsContext);
    if (context === undefined) {
        console.error('useStatistics must be used within a StatisticsProvider');
        // Return default values instead of throwing error
        return {
            refreshTrigger: 0,
            triggerRefresh: ()=>console.warn('StatisticsProvider not found')
        };
    }
    return context;
}
_s1(useStatistics, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useStatisticsRefresh() {
    _s2();
    const { triggerRefresh } = useStatistics();
    return triggerRefresh;
}
_s2(useStatisticsRefresh, "neIfD2Rj6TbS1DLsuPeUHu9omyM=", false, function() {
    return [
        useStatistics
    ];
});
var _c;
__turbopack_context__.k.register(_c, "StatisticsProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/providers.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Providers": (()=>Providers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$auth$2d$wrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/auth-wrapper.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$contexts$2f$statistics$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/contexts/statistics-context.tsx [app-client] (ecmascript)");
"use client";
;
;
;
;
function Providers({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        attribute: "class",
        defaultTheme: "system",
        enableSystem: true,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$auth$2d$wrapper$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthWrapper"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$contexts$2f$statistics$2d$context$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StatisticsProvider"], {
                children: children
            }, void 0, false, {
                fileName: "[project]/src/app/providers.tsx",
                lineNumber: 16,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/providers.tsx",
            lineNumber: 15,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/providers.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
}
_c = Providers;
var _c;
__turbopack_context__.k.register(_c, "Providers");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/utils/json-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Utility functions for safe JSON operations
 */ /**
 * Safely parse JSON string, returns null if parsing fails or input is invalid
 */ __turbopack_context__.s({
    "cleanupLocalStorage": (()=>cleanupLocalStorage),
    "safeJsonParse": (()=>safeJsonParse),
    "safeJsonStringify": (()=>safeJsonStringify),
    "safeLocalStorageGet": (()=>safeLocalStorageGet),
    "safeLocalStorageRemove": (()=>safeLocalStorageRemove),
    "safeLocalStorageSet": (()=>safeLocalStorageSet)
});
const safeJsonParse = (jsonString)=>{
    if (!jsonString || jsonString === 'undefined' || jsonString === 'null' || jsonString.trim() === '') {
        return null;
    }
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('Failed to parse JSON:', error, 'Input:', jsonString);
        return null;
    }
};
const safeJsonStringify = (obj)=>{
    try {
        return JSON.stringify(obj);
    } catch (error) {
        console.warn('Failed to stringify JSON:', error, 'Object:', obj);
        return '';
    }
};
const safeLocalStorageGet = (key)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        const item = localStorage.getItem(key);
        return safeJsonParse(item);
    } catch (error) {
        console.warn('Failed to get from localStorage:', error, 'Key:', key);
        return null;
    }
};
const safeLocalStorageSet = (key, value)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        const jsonString = safeJsonStringify(value);
        if (jsonString) {
            localStorage.setItem(key, jsonString);
            return true;
        }
        return false;
    } catch (error) {
        console.warn('Failed to set to localStorage:', error, 'Key:', key, 'Value:', value);
        return false;
    }
};
const safeLocalStorageRemove = (key)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.warn('Failed to remove from localStorage:', error, 'Key:', key);
        return false;
    }
};
const cleanupLocalStorage = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    let cleanedCount = 0;
    const keysToRemove = [];
    try {
        for(let i = 0; i < localStorage.length; i++){
            const key = localStorage.key(i);
            if (key) {
                const value = localStorage.getItem(key);
                if (value === 'undefined' || value === 'null' || value === '') {
                    keysToRemove.push(key);
                }
            }
        }
        keysToRemove.forEach((key)=>{
            localStorage.removeItem(key);
            cleanedCount++;
        });
        if (cleanedCount > 0) {
            console.log(`Cleaned up ${cleanedCount} invalid localStorage items`);
        }
    } catch (error) {
        console.warn('Failed to cleanup localStorage:', error);
    }
    return cleanedCount;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/app-initializer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AppInitializer": (()=>AppInitializer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$json$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/json-utils.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function AppInitializer() {
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AppInitializer.useEffect": ()=>{
            // Cleanup localStorage khi app khởi động
            const cleanedCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$json$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanupLocalStorage"])();
            if (cleanedCount > 0) {
                console.log(`AppInitializer: Đã dọn dẹp ${cleanedCount} mục localStorage không hợp lệ`);
            }
            // Global error handler cho unhandled promise rejections
            const handleUnhandledRejection = {
                "AppInitializer.useEffect.handleUnhandledRejection": (event)=>{
                    if (event.reason && event.reason.message) {
                        const message = event.reason.message;
                        if (message.includes('not valid JSON') || message.includes('"undefined" is not valid JSON')) {
                            console.warn('Caught JSON parsing error in promise:', event.reason);
                            event.preventDefault(); // Prevent the error from being logged to console
                            return;
                        }
                    }
                }
            }["AppInitializer.useEffect.handleUnhandledRejection"];
            // Global error handler cho window errors
            const handleError = {
                "AppInitializer.useEffect.handleError": (event)=>{
                    if (event.message) {
                        if (event.message.includes('not valid JSON') || event.message.includes('"undefined" is not valid JSON')) {
                            console.warn('Caught JSON parsing error:', event.message);
                            event.preventDefault();
                            return;
                        }
                    }
                }
            }["AppInitializer.useEffect.handleError"];
            // Intercept console.error để filter các lỗi JSON parsing và empty src
            const originalConsoleError = console.error;
            console.error = ({
                "AppInitializer.useEffect": (...args)=>{
                    const message = args.join(' ');
                    // Suppress JSON parsing errors
                    if (message.includes('"undefined" is not valid JSON') || message.includes('not valid JSON')) {
                        console.warn('Suppressed JSON parsing error:', ...args);
                        return;
                    }
                    // Suppress empty src attribute warnings
                    if (message.includes('An empty string ("") was passed to the src attribute')) {
                        console.debug('Suppressed empty src warning:', ...args);
                        return;
                    }
                    // Call original console.error for other errors
                    originalConsoleError.apply(console, args);
                }
            })["AppInitializer.useEffect"];
            // Add event listeners
            window.addEventListener('unhandledrejection', handleUnhandledRejection);
            window.addEventListener('error', handleError);
            // Cleanup function
            return ({
                "AppInitializer.useEffect": ()=>{
                    window.removeEventListener('unhandledrejection', handleUnhandledRejection);
                    window.removeEventListener('error', handleError);
                    // Restore original console.error
                    console.error = originalConsoleError;
                }
            })["AppInitializer.useEffect"];
        }
    }["AppInitializer.useEffect"], []);
    // Component này không render gì
    return null;
}
_s(AppInitializer, "OD7bBpZva5O2jO+Puf00hKivP7c=");
_c = AppInitializer;
var _c;
__turbopack_context__.k.register(_c, "AppInitializer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    }, specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, Error("react-stack-top-frame"), createTask(getTaskName(type)));
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>J),
    "useTheme": (()=>z)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
"use client";
;
var M = (e, i, s, u, m, a, l, h)=>{
    let d = document.documentElement, w = [
        "light",
        "dark"
    ];
    function p(n) {
        (Array.isArray(e) ? e : [
            e
        ]).forEach((y)=>{
            let k = y === "class", S = k && a ? m.map((f)=>a[f] || f) : m;
            k ? (d.classList.remove(...S), d.classList.add(a && a[n] ? a[n] : n)) : d.setAttribute(y, n);
        }), R(n);
    }
    function R(n) {
        h && w.includes(n) && (d.style.colorScheme = n);
    }
    function c() {
        return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
    if (u) p(u);
    else try {
        let n = localStorage.getItem(i) || s, y = l && n === "system" ? c() : n;
        p(y);
    } catch (n) {}
};
var b = [
    "light",
    "dark"
], I = "(prefers-color-scheme: dark)", O = typeof window == "undefined", x = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(void 0), U = {
    setTheme: (e)=>{},
    themes: []
}, z = ()=>{
    var e;
    return (e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(x)) != null ? e : U;
}, J = (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(x) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, e.children) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(V, {
        ...e
    }), N = [
    "light",
    "dark"
], V = ({ forcedTheme: e, disableTransitionOnChange: i = !1, enableSystem: s = !0, enableColorScheme: u = !0, storageKey: m = "theme", themes: a = N, defaultTheme: l = s ? "system" : "light", attribute: h = "data-theme", value: d, children: w, nonce: p, scriptProps: R })=>{
    let [c, n] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "V.useState": ()=>H(m, l)
    }["V.useState"]), [T, y] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "V.useState": ()=>c === "system" ? E() : c
    }["V.useState"]), k = d ? Object.values(d) : a, S = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "V.useCallback[S]": (o)=>{
            let r = o;
            if (!r) return;
            o === "system" && s && (r = E());
            let v = d ? d[r] : r, C = i ? W(p) : null, P = document.documentElement, L = {
                "V.useCallback[S].L": (g)=>{
                    g === "class" ? (P.classList.remove(...k), v && P.classList.add(v)) : g.startsWith("data-") && (v ? P.setAttribute(g, v) : P.removeAttribute(g));
                }
            }["V.useCallback[S].L"];
            if (Array.isArray(h) ? h.forEach(L) : L(h), u) {
                let g = b.includes(l) ? l : null, D = b.includes(r) ? r : g;
                P.style.colorScheme = D;
            }
            C == null || C();
        }
    }["V.useCallback[S]"], [
        p
    ]), f = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "V.useCallback[f]": (o)=>{
            let r = typeof o == "function" ? o(c) : o;
            n(r);
            try {
                localStorage.setItem(m, r);
            } catch (v) {}
        }
    }["V.useCallback[f]"], [
        c
    ]), A = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "V.useCallback[A]": (o)=>{
            let r = E(o);
            y(r), c === "system" && s && !e && S("system");
        }
    }["V.useCallback[A]"], [
        c,
        e
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "V.useEffect": ()=>{
            let o = window.matchMedia(I);
            return o.addListener(A), A(o), ({
                "V.useEffect": ()=>o.removeListener(A)
            })["V.useEffect"];
        }
    }["V.useEffect"], [
        A
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "V.useEffect": ()=>{
            let o = {
                "V.useEffect.o": (r)=>{
                    r.key === m && (r.newValue ? n(r.newValue) : f(l));
                }
            }["V.useEffect.o"];
            return window.addEventListener("storage", o), ({
                "V.useEffect": ()=>window.removeEventListener("storage", o)
            })["V.useEffect"];
        }
    }["V.useEffect"], [
        f
    ]), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "V.useEffect": ()=>{
            S(e != null ? e : c);
        }
    }["V.useEffect"], [
        e,
        c
    ]);
    let Q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "V.useMemo[Q]": ()=>({
                theme: c,
                setTheme: f,
                forcedTheme: e,
                resolvedTheme: c === "system" ? T : c,
                themes: s ? [
                    ...a,
                    "system"
                ] : a,
                systemTheme: s ? T : void 0
            })
    }["V.useMemo[Q]"], [
        c,
        f,
        e,
        T,
        s,
        a
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(x.Provider, {
        value: Q
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(_, {
        forcedTheme: e,
        storageKey: m,
        attribute: h,
        enableSystem: s,
        enableColorScheme: u,
        defaultTheme: l,
        value: d,
        themes: a,
        nonce: p,
        scriptProps: R
    }), w);
}, _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(({ forcedTheme: e, storageKey: i, attribute: s, enableSystem: u, enableColorScheme: m, defaultTheme: a, value: l, themes: h, nonce: d, scriptProps: w })=>{
    let p = JSON.stringify([
        s,
        i,
        a,
        e,
        h,
        l,
        u,
        m
    ]).slice(1, -1);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("script", {
        ...w,
        suppressHydrationWarning: !0,
        nonce: typeof window == "undefined" ? d : "",
        dangerouslySetInnerHTML: {
            __html: `(${M.toString()})(${p})`
        }
    });
}), H = (e, i)=>{
    if (O) return;
    let s;
    try {
        s = localStorage.getItem(e) || void 0;
    } catch (u) {}
    return s || i;
}, W = (e)=>{
    let i = document.createElement("style");
    return e && i.setAttribute("nonce", e), i.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")), document.head.appendChild(i), ()=>{
        window.getComputedStyle(document.body), setTimeout(()=>{
            document.head.removeChild(i);
        }, 1);
    };
}, E = (e)=>(e || (e = window.matchMedia(I)), e.matches ? "dark" : "light");
;
}}),
"[project]/node_modules/next/navigation.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/navigation.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=_9e817198._.js.map