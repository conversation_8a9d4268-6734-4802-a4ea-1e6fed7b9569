// Test script để kiểm tra email notifications với email thật
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testRealEmail() {
  console.log('🧪 Testing Real Email Notifications...\n');

  try {
    // 1. Test đăng ký email với email thật
    console.log('1️⃣ Testing real email subscription...');
    const subscribeResponse = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>', // Email thật để test
      name: 'Hung Test',
      taskReminders: true,
      dailySummary: true,
      weeklyReport: false,
      reminderHours: 1 // 1 giờ trước để test nhanh
    });
    
    console.log('✅ Real email subscription successful:', subscribeResponse.data);

    // 2. Tạo một task với due date gần để test reminder
    console.log('\n2️⃣ Creating a test task with near due date...');
    
    // Đăng nhập trước để tạo task
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123' // Thay bằng password thật nếu có account
    }).catch(err => {
      console.log('⚠️ Login failed, will create task without auth');
      return null;
    });

    if (loginResponse) {
      const token = loginResponse.data.access_token;
      
      // Tạo task với due date trong 2 giờ tới
      const dueDate = new Date();
      dueDate.setHours(dueDate.getHours() + 2);
      
      const taskResponse = await axios.post(`${API_BASE}/tasks`, {
        title: 'Test Email Reminder Task',
        description: 'Task này được tạo để test email reminder',
        dueDate: dueDate.toISOString(),
        priority: 'high'
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('✅ Test task created:', taskResponse.data);
    }

    // 3. Test manual scheduler để gửi email ngay
    console.log('\n3️⃣ Testing manual email sending...');
    const schedulerResponse = await axios.post(`${API_BASE}/notifications/test-scheduler`);
    
    console.log('✅ Manual scheduler test:', schedulerResponse.data);
    console.log('\n📧 Check your email inbox for notifications!');

  } catch (error) {
    console.error('❌ Error testing real email notifications:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Chạy test
testRealEmail();
