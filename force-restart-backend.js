// Script để force restart backend và kiểm tra config
const { spawn } = require('child_process');
const axios = require('axios');
const path = require('path');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function forceRestartBackend() {
  console.log('🔄 Force Restart Backend và kiểm tra config...\n');

  try {
    console.log('1️⃣ Kiểm tra backend hiện tại...');
    
    try {
      await axios.get(`${API_BASE.replace('/api', '')}/`);
      console.log('⚠️ Backend đang chạy - cần restart để load config mới');
    } catch (error) {
      console.log('ℹ️ Backend không chạy - sẽ khởi động mới');
    }

    console.log('\n2️⃣ Hướng dẫn restart backend:');
    console.log('🔧 Thực hiện các bước sau:');
    console.log('');
    console.log('   1. Mở terminal/command prompt');
    console.log('   2. cd deloybe');
    console.log('   3. Dừng backend hiện tại (Ctrl+C nếu đang chạy)');
    console.log('   4. npm run start:dev');
    console.log('   5. Chờ thấy log "Email Service Configuration"');
    console.log('');

    console.log('3️⃣ Kiểm tra logs khi backend khởi động:');
    console.log('Phải thấy các dòng sau:');
    console.log('');
    console.log('   📧 Email Service Configuration:');
    console.log('      EMAIL_USER: <EMAIL>');
    console.log('      FRONTEND_URL: https://qltime.vercel.app');
    console.log('      Final frontend URL: https://qltime.vercel.app');
    console.log('');

    console.log('4️⃣ Sau khi restart, chạy test:');
    console.log('   node test-production-email.js');
    console.log('');

    console.log('5️⃣ Nếu vẫn lỗi, kiểm tra:');
    console.log('   - File .env có đúng FRONTEND_URL=https://qltime.vercel.app');
    console.log('   - Không có file .env.local hoặc .env.production ghi đè');
    console.log('   - Backend đã restart hoàn toàn');
    console.log('');

    // Kiểm tra file .env
    console.log('6️⃣ Kiểm tra file .env hiện tại:');
    const fs = require('fs');
    const envPath = path.join(__dirname, 'deloybe', '.env');
    
    try {
      const envContent = fs.readFileSync(envPath, 'utf8');
      const frontendUrlLine = envContent.split('\n').find(line => line.startsWith('FRONTEND_URL='));
      
      if (frontendUrlLine) {
        console.log(`   ✅ ${frontendUrlLine}`);
        if (frontendUrlLine.includes('https://qltime.vercel.app')) {
          console.log('   ✅ URL đúng trong .env');
        } else {
          console.log('   ❌ URL sai trong .env!');
        }
      } else {
        console.log('   ❌ Không tìm thấy FRONTEND_URL trong .env');
      }
    } catch (error) {
      console.log('   ⚠️ Không thể đọc file .env:', error.message);
    }

    console.log('\n🎯 Mục tiêu: Email phải có links dẫn đến https://qltime.vercel.app');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

forceRestartBackend();
