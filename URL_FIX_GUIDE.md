# Hướng dẫn sửa lỗi URL trong Email

## Vấn đề
Email chào mừng có các link dẫn đến `http://localhost:3000` thay vì `https://qltime.vercel.app`

## Nguyên nhân
1. File `.env` có `FRONTEND_URL=https://qltime.vercel.app/` (có dấu `/` ở cuối)
2. Backend chưa được restart sau khi thay đổi `.env`

## Giải pháp đã thực hiện

### 1. Sửa file .env
```env
# Trước (SAI)
FRONTEND_URL=https://qltime.vercel.app/

# Sau (ĐÚNG)  
FRONTEND_URL=https://qltime.vercel.app
```

### 2. Thêm helper method trong EmailService
- `getFrontendUrl()`: Tự động loại bỏ dấu `/` ở cuối
- `logConfiguration()`: <PERSON>g cấu hình khi khởi động

### 3. Cậ<PERSON> nhật tất cả email templates
- `sendWelcomeEmail()`
- `sendWelcomeEmailPublic()` ← Template bạn đang thấy
- `sendTaskReminderEmail()`
- `sendTaskReminderEmailPublic()`

## Cách kiểm tra và test

### Bước 1: Restart Backend
```bash
cd deloybe
npm run start:dev
```

### Bước 2: Kiểm tra logs
Khi backend khởi động, bạn sẽ thấy:
```
📧 Email Service Configuration:
   EMAIL_USER: <EMAIL>
   FRONTEND_URL: https://qltime.vercel.app
   Final frontend URL: https://qltime.vercel.app
```

### Bước 3: Test email
```bash
# Kiểm tra cấu hình
node check-backend-config.js

# Test email với URL mới
node test-email-url-fix.js
```

### Bước 4: Kiểm tra email thật
Sau khi chạy test, kiểm tra email `<EMAIL>`:
- ✅ "Khám phá QLTime" → `https://qltime.vercel.app`
- ✅ "Tạo tài khoản miễn phí" → `https://qltime.vercel.app/register`
- ✅ "Hủy đăng ký" → `https://qltime.vercel.app/unsubscribe?token=...`

## Kết quả mong đợi

### Email template sẽ tạo ra:
```html
<a href="https://qltime.vercel.app">Khám phá QLTime</a>
<a href="https://qltime.vercel.app/register">Tạo tài khoản miễn phí</a>
<a href="https://qltime.vercel.app/unsubscribe?token=abc123">Hủy đăng ký</a>
```

### Thay vì (lỗi cũ):
```html
<a href="http://localhost:3000">Khám phá QLTime</a>
<a href="http://localhost:3000/register">Tạo tài khoản miễn phí</a>
```

## Debug nếu vẫn lỗi

### 1. Kiểm tra file .env
```bash
cd deloybe
cat .env | grep FRONTEND_URL
# Phải thấy: FRONTEND_URL=https://qltime.vercel.app
```

### 2. Kiểm tra backend logs
Tìm dòng log:
```
📧 Email Service Configuration:
   FRONTEND_URL: https://qltime.vercel.app
```

### 3. Kiểm tra email service
```bash
# Test API trực tiếp
curl -X POST http://localhost:3001/api/notifications/email/subscribe-public \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "name": "Test User",
    "taskReminders": true
  }'
```

## Lưu ý quan trọng

1. **Phải restart backend** sau khi thay đổi `.env`
2. **Kiểm tra logs** để đảm bảo cấu hình đúng
3. **Test với email thật** để xác nhận URL
4. **Xóa cache browser** nếu cần

Sau khi thực hiện các bước trên, tất cả email sẽ có URL đúng dẫn đến `https://qltime.vercel.app`!
