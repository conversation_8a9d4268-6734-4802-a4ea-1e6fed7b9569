"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const ai_service_1 = require("./ai.service");
const chat_dto_1 = require("./dto/chat.dto");
let AIController = class AIController {
    aiService;
    constructor(aiService) {
        this.aiService = aiService;
    }
    async chat(chatRequest, user) {
        return this.aiService.chat(user._id.toString(), chatRequest);
    }
    async suggestPriority(taskSuggestion) {
        const priority = await this.aiService.suggestPriority(taskSuggestion);
        return { priority };
    }
    async suggestDueDate(taskSuggestion) {
        const dueDate = await this.aiService.suggestDueDate(taskSuggestion);
        return { dueDate };
    }
    async getChatHistory(user, sessionId, limit) {
        return this.aiService.getChatHistory(user._id.toString(), sessionId, limit ? parseInt(limit.toString()) : 50);
    }
    async clearChatHistory(user, sessionId) {
        await this.aiService.clearChatHistory(user._id.toString(), sessionId);
        return {
            message: sessionId
                ? 'Đã xóa lịch sử chat của phiên này'
                : 'Đã xóa toàn bộ lịch sử chat'
        };
    }
};
exports.AIController = AIController;
__decorate([
    (0, common_1.Post)('chat'),
    (0, swagger_1.ApiOperation)({ summary: 'Chat với AI' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Phản hồi từ AI',
        type: chat_dto_1.ChatResponseDto
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dữ liệu không hợp lệ' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chat_dto_1.ChatRequestDto, Object]),
    __metadata("design:returntype", Promise)
], AIController.prototype, "chat", null);
__decorate([
    (0, common_1.Post)('suggest-priority'),
    (0, swagger_1.ApiOperation)({ summary: 'Gợi ý độ ưu tiên cho công việc' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Độ ưu tiên được gợi ý',
        schema: {
            type: 'object',
            properties: {
                priority: {
                    type: 'string',
                    enum: ['high', 'medium', 'low']
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dữ liệu không hợp lệ' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chat_dto_1.TaskSuggestionDto]),
    __metadata("design:returntype", Promise)
], AIController.prototype, "suggestPriority", null);
__decorate([
    (0, common_1.Post)('suggest-due-date'),
    (0, swagger_1.ApiOperation)({ summary: 'Gợi ý ngày hoàn thành cho công việc' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Ngày hoàn thành được gợi ý',
        schema: {
            type: 'object',
            properties: {
                dueDate: {
                    type: 'string',
                    format: 'date',
                    nullable: true
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dữ liệu không hợp lệ' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [chat_dto_1.TaskSuggestionDto]),
    __metadata("design:returntype", Promise)
], AIController.prototype, "suggestDueDate", null);
__decorate([
    (0, common_1.Get)('chat-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy lịch sử chat' }),
    (0, swagger_1.ApiQuery)({ name: 'sessionId', required: false, description: 'ID phiên chat' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Số lượng tin nhắn tối đa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lịch sử chat',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    role: { type: 'string', enum: ['user', 'assistant'] },
                    content: { type: 'string' },
                    timestamp: { type: 'string', format: 'date-time' },
                    sessionId: { type: 'string' }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)('sessionId')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Number]),
    __metadata("design:returntype", Promise)
], AIController.prototype, "getChatHistory", null);
__decorate([
    (0, common_1.Delete)('chat-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Xóa lịch sử chat' }),
    (0, swagger_1.ApiQuery)({ name: 'sessionId', required: false, description: 'ID phiên chat (nếu không có sẽ xóa tất cả)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Xóa thành công' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)('sessionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AIController.prototype, "clearChatHistory", null);
exports.AIController = AIController = __decorate([
    (0, swagger_1.ApiTags)('AI'),
    (0, common_1.Controller)('api/ai'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [ai_service_1.AIService])
], AIController);
//# sourceMappingURL=ai.controller.js.map