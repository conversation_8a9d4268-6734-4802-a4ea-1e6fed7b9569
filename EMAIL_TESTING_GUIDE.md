# Hướng dẫn Test Email Notifications

## Vấn đề hiện tại
- Email server không hoạt động
- Cần test hệ thống gửi email với dữ liệu user thật

## Giải pháp đã implement

### 1. Đã thêm method test mới
- `testSendEmailWithUserData()` trong `NotificationsService`
- L<PERSON>y dữ liệu task từ user ID hiện tại
- Gửi email về địa chỉ đã đăng ký

### 2. Đã thêm API endpoints mới
- `POST /api/notifications/test-email-with-user-data` (cần auth)
- `POST /api/notifications/test-email-with-user-data/:userId` (không cần auth)

### 3. Đã cải thiện logging
- Email service có log chi tiết hơn
- Hiển thị rõ email credentials và quá trình gửi

## Cách test

### Bước 1: Ki<PERSON>m tra cấu hình email
```bash
node check-email-config.js
```

### Bước 2: Test với user data thật
```bash
# Test đầy đủ (tạo user, task, subscription)
node test-email-with-user-data.js

# Hoặc test đơn giản với user ID có sẵn
node test-email-simple.js --list-users  # Lấy user ID
node test-email-simple.js               # Test với user ID
```

### Bước 3: Kiểm tra backend logs
Xem console của backend server để kiểm tra:
- Email credentials có được load không
- Quá trình gửi email có lỗi gì không

## Cấu hình email cần thiết

### File .env trong backend (deloybe/)
```env
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
FRONTEND_URL=https://qltime.vercel.app
```

### Cách tạo Gmail App Password
1. Bật 2-Factor Authentication cho Gmail
2. Vào Google Account Settings
3. Security > 2-Step Verification > App passwords
4. Tạo app password mới cho "Mail"
5. Sử dụng password này trong EMAIL_PASSWORD

## Luồng hoạt động mới

1. **User đăng ký email notification** với user ID của họ
2. **Hệ thống lưu subscription** liên kết với user ID
3. **Khi gửi thông báo**: 
   - Lấy tasks từ user ID (không phải từ email)
   - Gửi về email đã đăng ký
4. **Email chứa dữ liệu thật** của user đó

## Test cases

### Case 1: User có tasks sắp hết hạn
- Tạo user và tasks
- Đăng ký email notification
- Gửi email với danh sách tasks

### Case 2: User không có tasks
- Gửi email thông báo không có tasks sắp hết hạn

### Case 3: User chưa có subscription
- Trả về thông báo cần đăng ký email

## Debug

### Nếu email không gửi được:
1. Kiểm tra backend logs
2. Verify Gmail credentials
3. Kiểm tra network/firewall
4. Test với email khác

### Nếu không có tasks:
1. Tạo tasks với dueDate trong 48h tới
2. Kiểm tra user ID đúng không
3. Verify tasks được lưu trong database

## API Examples

### Đăng ký email (cần auth)
```bash
curl -X POST http://localhost:3001/api/notifications/email/subscribe \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "taskReminders": true,
    "reminderHours": 24
  }'
```

### Test gửi email (cần auth)
```bash
curl -X POST http://localhost:3001/api/notifications/test-email-with-user-data \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Test với user ID cụ thể (không cần auth)
```bash
curl -X POST http://localhost:3001/api/notifications/test-email-with-user-data/USER_ID
```

## Kết quả mong đợi

Email sẽ chứa:
- Thông tin user (tên, email)
- Danh sách tasks sắp hết hạn của user đó
- Links đến QLTime frontend
- Unsubscribe link

Hệ thống sẽ hoạt động theo đúng yêu cầu: **lấy dữ liệu từ user ID hiện tại, gửi về email đã đăng ký**.
