// Script để kiểm tra tất cả file .env có thể ghi đè config
const fs = require('fs');
const path = require('path');

function checkEnvFiles() {
  console.log('🔍 Kiểm tra tất cả file .env...\n');

  const backendDir = path.join(__dirname, 'deloybe');
  const envFiles = [
    '.env',
    '.env.local',
    '.env.development',
    '.env.production',
    '.env.development.local',
    '.env.production.local'
  ];

  console.log('📁 Thư mục backend:', backendDir);
  console.log('');

  envFiles.forEach(fileName => {
    const filePath = path.join(backendDir, fileName);
    
    try {
      if (fs.existsSync(filePath)) {
        console.log(`✅ Tìm thấy: ${fileName}`);
        const content = fs.readFileSync(filePath, 'utf8');
        const frontendUrlLine = content.split('\n').find(line => line.startsWith('FRONTEND_URL='));
        
        if (frontendUrlLine) {
          console.log(`   📧 ${frontendUrlLine}`);
          if (frontendUrlLine.includes('localhost')) {
            console.log('   ❌ CHỨA LOCALHOST - ĐÂY CÓ THỂ LÀ NGUYÊN NHÂN!');
          } else if (frontendUrlLine.includes('https://qltime.vercel.app')) {
            console.log('   ✅ URL đúng');
          } else {
            console.log('   ⚠️ URL khác');
          }
        } else {
          console.log('   ℹ️ Không có FRONTEND_URL');
        }
        console.log('');
      } else {
        console.log(`❌ Không tồn tại: ${fileName}`);
      }
    } catch (error) {
      console.log(`❌ Lỗi đọc ${fileName}:`, error.message);
    }
  });

  console.log('🔧 Thứ tự ưu tiên file .env (NestJS):');
  console.log('   1. .env.development.local (cao nhất)');
  console.log('   2. .env.local');
  console.log('   3. .env.development');
  console.log('   4. .env (thấp nhất)');
  console.log('');

  console.log('💡 Giải pháp nếu có file ghi đè:');
  console.log('   - Xóa file .env.local nếu có FRONTEND_URL=localhost');
  console.log('   - Hoặc sửa file đó thành FRONTEND_URL=https://qltime.vercel.app');
  console.log('   - Restart backend sau khi thay đổi');

  // Kiểm tra Dockerfile
  console.log('\n🐳 Kiểm tra Dockerfile:');
  const dockerfilePath = path.join(backendDir, 'Dockerfile');
  
  try {
    if (fs.existsSync(dockerfilePath)) {
      const dockerContent = fs.readFileSync(dockerfilePath, 'utf8');
      const frontendUrlLines = dockerContent.split('\n').filter(line => line.includes('FRONTEND_URL'));
      
      if (frontendUrlLines.length > 0) {
        console.log('   📧 FRONTEND_URL trong Dockerfile:');
        frontendUrlLines.forEach(line => {
          console.log(`   ${line.trim()}`);
          if (line.includes('localhost')) {
            console.log('   ❌ DOCKERFILE CHỨA LOCALHOST!');
          }
        });
      } else {
        console.log('   ℹ️ Không có FRONTEND_URL trong Dockerfile');
      }
    }
  } catch (error) {
    console.log('   ⚠️ Không thể đọc Dockerfile:', error.message);
  }
}

checkEnvFiles();
