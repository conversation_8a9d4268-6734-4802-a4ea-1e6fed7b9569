// Test email với production URL
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testProductionEmail() {
  console.log('🧪 Test Production Email URL...\n');

  try {
    console.log('📧 Gửi email test đến <EMAIL>...');
    
    const response = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>',
      name: 'Production URL Test - ' + new Date().toLocaleString('vi-VN'),
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Email sent successfully:', response.data);
    
    console.log('\n📧 Kiểm <NAME_EMAIL> ngay bây giờ!');
    console.log('\n🔗 Các link trong email PHẢI là:');
    console.log('   ✅ Khám phá QLTime: https://qltime.vercel.app');
    console.log('   ✅ Tạo tài khoản miễn phí: https://qltime.vercel.app/register');
    console.log('   ✅ Hủy đăng ký: https://qltime.vercel.app/unsubscribe?token=...');
    
    console.log('\n❌ Nếu vẫn thấy:');
    console.log('   ❌ http://localhost:3000');
    console.log('   ❌ http://localhost:3000/register');
    
    console.log('\n🔧 Thì cần:');
    console.log('   1. Kiểm tra backend logs có thấy "Final frontend URL: https://qltime.vercel.app"');
    console.log('   2. Nếu không thấy → restart backend');
    console.log('   3. Nếu thấy rồi mà vẫn lỗi → có thể cache email client');

    console.log('\n⏰ Thời gian gửi:', new Date().toLocaleString('vi-VN'));

  } catch (error) {
    console.error('❌ Error sending production email:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testProductionEmail();
