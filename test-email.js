// Test script để kiểm tra email notifications
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testEmailNotifications() {
  console.log('🧪 Testing Email Notifications...\n');

  try {
    // 1. Test đăng ký email public (không cần auth)
    console.log('1️⃣ Testing public email subscription...');
    const subscribeResponse = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>',
      name: 'Test User',
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 24
    });
    
    console.log('✅ Public subscription successful:', subscribeResponse.data);

    // 2. Test manual scheduler (gửi thông báo thủ công)
    console.log('\n2️⃣ Testing manual scheduler...');
    const schedulerResponse = await axios.post(`${API_BASE}/notifications/test-scheduler`);
    
    console.log('✅ Manual scheduler test:', schedulerResponse.data);

    // 3. <PERSON><PERSON><PERSON> tra logs
    console.log('\n3️⃣ Check backend logs for email sending status...');
    
  } catch (error) {
    console.error('❌ Error testing email notifications:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Chạy test
testEmailNotifications();
