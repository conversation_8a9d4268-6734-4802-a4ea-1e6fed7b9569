// Test với email thật <EMAIL>
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testWithRealEmail() {
  console.log('📧 Test với email thật <EMAIL>...\n');

  try {
    console.log('📧 Gửi email test đến: <EMAIL>');
    console.log('👀 QUAN TRỌNG: Mở console backend và theo dõi logs!');
    console.log('');

    const timestamp = new Date().toLocaleString('vi-VN');

    const response = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: '<EMAIL>',
      name: `URL Debug Test - ${timestamp}`,
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Email gửi thành công!');
    console.log('Response:', response.data);
    console.log('');

    console.log('🔍 KIỂM TRA NGAY:');
    console.log('');
    console.log('1️⃣ Backend console logs - tìm dòng:');
    console.log('   📧 URLs found in email HTML:');
    console.log('   Phải thấy:');
    console.log('   ✅ href="https://qltime.vercel.app"');
    console.log('   ✅ href="https://qltime.vercel.app/register"');
    console.log('   ❌ KHÔNG được thấy: href="http://localhost:3000"');
    console.log('');

    console.log('2️⃣ Email <EMAIL>:');
    console.log('   - Mở Gmail');
    console.log('   - Tìm email mới nhất');
    console.log('   - Subject: "Chào mừng bạn đến với QLTime - Đăng ký thành công!"');
    console.log('   - Name: "URL Debug Test - ' + timestamp + '"');
    console.log('');

    console.log('3️⃣ Kiểm tra URL trong email:');
    console.log('   - Click chuột phải vào button "Tạo tài khoản miễn phí"');
    console.log('   - Chọn "Copy link address" hoặc "Sao chép địa chỉ liên kết"');
    console.log('   - Paste URL vào notepad để kiểm tra');
    console.log('');

    console.log('🎯 KẾT QUẢ MONG ĐỢI:');
    console.log('   ✅ https://qltime.vercel.app/register');
    console.log('   ❌ http://localhost:3000/register');
    console.log('');

    console.log('📋 Phân tích kết quả:');
    console.log('');
    console.log('   Nếu backend logs hiển thị https://qltime.vercel.app:');
    console.log('   + Email vẫn có localhost → Cache email client');
    console.log('   + Email có URL đúng → Đã sửa thành công!');
    console.log('');
    console.log('   Nếu backend logs hiển thị localhost:');
    console.log('   + Backend chưa restart → Restart backend');
    console.log('   + File .env sai → Kiểm tra lại .env');
    console.log('');

    console.log('⏰ Thời gian test:', new Date().toLocaleString('vi-VN'));

  } catch (error) {
    console.error('❌ Error sending test email:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testWithRealEmail();
