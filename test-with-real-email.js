// Test với email thật của bạn
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testWithRealEmail() {
  console.log('📧 Test với email thật của bạn...\n');

  // THAY ĐỔI EMAIL NÀY THÀNH EMAIL THẬT CỦA BẠN
  const YOUR_REAL_EMAIL = '<EMAIL>'; // <-- THAY ĐỔI TẠI ĐÂY
  
  if (YOUR_REAL_EMAIL === '<EMAIL>') {
    console.log('❌ Vui lòng thay đổi YOUR_REAL_EMAIL trong script này!');
    console.log('   Mở file test-with-real-email.js');
    console.log('   Sửa dòng: const YOUR_REAL_EMAIL = "<EMAIL>"');
    console.log('   Thành: const YOUR_REAL_EMAIL = "<EMAIL>"');
    return;
  }

  try {
    console.log(`📧 Gửi email test đến: ${YOUR_REAL_EMAIL}`);
    console.log('👀 QUAN TRỌNG: Mở console backend và theo dõi logs!');
    console.log('');

    const timestamp = new Date().toLocaleString('vi-VN');
    
    const response = await axios.post(`${API_BASE}/notifications/email/subscribe-public`, {
      email: YOUR_REAL_EMAIL,
      name: `URL Test - ${timestamp}`,
      taskReminders: true,
      dailySummary: false,
      weeklyReport: false,
      reminderHours: 1
    });
    
    console.log('✅ Email gửi thành công!');
    console.log('Response:', response.data);
    console.log('');

    console.log('🔍 KIỂM TRA NGAY:');
    console.log('');
    console.log('1️⃣ Backend console logs - tìm dòng:');
    console.log('   📧 URLs found in email HTML:');
    console.log('   Phải thấy:');
    console.log('   ✅ href="https://qltime.vercel.app"');
    console.log('   ✅ href="https://qltime.vercel.app/register"');
    console.log('   ❌ KHÔNG được thấy: href="http://localhost:3000"');
    console.log('');

    console.log(`2️⃣ Email ${YOUR_REAL_EMAIL}:`);
    console.log('   - Mở email mới nhất');
    console.log('   - Subject: "Chào mừng bạn đến với QLTime - Đăng ký thành công!"');
    console.log('   - Click chuột phải vào "Tạo tài khoản miễn phí"');
    console.log('   - Chọn "Copy link address"');
    console.log('   - Kiểm tra URL');
    console.log('');

    console.log('🎯 KẾT QUẢ MONG ĐỢI:');
    console.log('   ✅ https://qltime.vercel.app/register');
    console.log('   ❌ http://localhost:3000/register');
    console.log('');

    console.log('📋 Nếu backend logs hiển thị URL đúng nhưng email vẫn localhost:');
    console.log('   - Cache email client');
    console.log('   - Thử mở email trên điện thoại');
    console.log('   - Thử email client khác');
    console.log('');

    console.log('📋 Nếu backend logs hiển thị localhost:');
    console.log('   - Backend chưa restart');
    console.log('   - File .env có vấn đề');
    console.log('   - Chạy: node check-env-files.js');

  } catch (error) {
    console.error('❌ Error sending test email:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

testWithRealEmail();
