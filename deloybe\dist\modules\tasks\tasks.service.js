"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const task_schema_1 = require("./schemas/task.schema");
let TasksService = class TasksService {
    taskModel;
    constructor(taskModel) {
        this.taskModel = taskModel;
    }
    async create(createTaskDto, userId) {
        const newTask = new this.taskModel({
            ...createTaskDto,
            user: userId,
        });
        return newTask.save();
    }
    async findAll(userId, filters = {}) {
        const query = { user: userId };
        if (filters.completed !== undefined) {
            query.completed = filters.completed === 'true';
        }
        if (filters.priority) {
            query.priority = filters.priority;
        }
        if (filters.status) {
            query.status = filters.status;
        }
        if (filters.category) {
            query.category = new mongoose_2.Types.ObjectId(filters.category);
        }
        if (filters.project) {
            query.project = new mongoose_2.Types.ObjectId(filters.project);
        }
        if (filters.dueDate) {
            const date = new Date(filters.dueDate);
            const startOfDay = new Date(date.setHours(0, 0, 0, 0));
            const endOfDay = new Date(date.setHours(23, 59, 59, 999));
            query.dueDate = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }
        return this.taskModel
            .find(query)
            .populate('category', 'name color')
            .populate('project', 'name')
            .sort({ createdAt: -1 })
            .exec();
    }
    async findById(id, userId) {
        const task = await this.taskModel
            .findById(id)
            .populate('category', 'name color')
            .populate('project', 'name')
            .exec();
        if (!task) {
            throw new common_1.NotFoundException(`Không tìm thấy công việc với ID: ${id}`);
        }
        if (task.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền truy cập công việc này');
        }
        return task;
    }
    async update(id, updateTaskDto, userId) {
        const task = await this.taskModel.findById(id);
        if (!task) {
            throw new common_1.NotFoundException(`Không tìm thấy công việc với ID: ${id}`);
        }
        if (task.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền cập nhật công việc này');
        }
        const updatedTask = await this.taskModel
            .findByIdAndUpdate(id, updateTaskDto, { new: true })
            .populate('category', 'name color')
            .populate('project', 'name')
            .exec();
        if (!updatedTask) {
            throw new common_1.NotFoundException(`Không tìm thấy công việc với ID: ${id}`);
        }
        return updatedTask;
    }
    async remove(id, userId) {
        const task = await this.taskModel.findById(id);
        if (!task) {
            throw new common_1.NotFoundException(`Không tìm thấy công việc với ID: ${id}`);
        }
        if (task.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền xóa công việc này');
        }
        await this.taskModel.findByIdAndDelete(id).exec();
    }
    async toggleCompletion(id, userId) {
        const task = await this.taskModel.findById(id);
        if (!task) {
            throw new common_1.NotFoundException(`Không tìm thấy công việc với ID: ${id}`);
        }
        if (task.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền cập nhật công việc này');
        }
        task.completed = !task.completed;
        return task.save();
    }
    async updateStatus(id, status, userId) {
        const task = await this.taskModel.findById(id);
        if (!task) {
            throw new common_1.NotFoundException(`Không tìm thấy công việc với ID: ${id}`);
        }
        if (task.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền cập nhật công việc này');
        }
        task.status = status;
        return task.save();
    }
    async findTasksDueSoon(userId, hours = 24) {
        const now = new Date();
        const futureTime = new Date();
        futureTime.setHours(futureTime.getHours() + hours);
        return this.taskModel.find({
            user: new mongoose_2.Types.ObjectId(userId),
            completed: false,
            dueDate: {
                $gte: now,
                $lte: futureTime,
            },
        }).sort({ dueDate: 1 }).exec();
    }
};
exports.TasksService = TasksService;
exports.TasksService = TasksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(task_schema_1.Task.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], TasksService);
//# sourceMappingURL=tasks.service.js.map