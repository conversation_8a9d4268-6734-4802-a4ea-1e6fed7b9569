// Test script đơn giản để kiểm tra email với user ID cụ thể
const axios = require('axios');

const API_BASE = process.env.API_URL || 'http://localhost:3001/api';

async function testEmailSimple() {
  console.log('🧪 Testing Email với user ID cụ thể...\n');

  try {
    // Thay đổi USER_ID này thành ID của user có sẵn trong database
    const USER_ID = '676a0b8b123456789abcdef0'; // Thay bằng user ID thật
    
    console.log(`📤 Testing email cho user ID: ${USER_ID}`);
    
    const response = await axios.post(
      `${API_BASE}/notifications/test-email-with-user-data/${USER_ID}`
    );
    
    console.log('✅ Response:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success) {
      console.log('\n🎉 Email test thành công!');
      console.log(`📧 Đã gửi ${response.data.results?.length || 0} email(s)`);
      console.log(`📋 Tìm thấy ${response.data.tasksFound} task(s) sắp hết hạn`);
    } else {
      console.log('\n⚠️ Email test không thành công:', response.data.message);
    }

  } catch (error) {
    console.error('❌ Error:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Hàm để lấy danh sách users (cần auth)
async function listUsers() {
  console.log('📋 Lấy danh sách users để tìm user ID...\n');
  
  try {
    // Đăng nhập để lấy token
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>', // Thay bằng email có sẵn
      password: 'password123'
    });
    
    const token = loginResponse.data.access_token;
    console.log('✅ Đăng nhập thành công');
    
    // Lấy thông tin user hiện tại
    const userResponse = await axios.get(`${API_BASE}/auth/profile`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('👤 User hiện tại:', {
      id: userResponse.data.id,
      name: userResponse.data.name,
      email: userResponse.data.email
    });
    
    console.log(`\n💡 Sử dụng user ID này: ${userResponse.data.id}`);
    console.log('Thay đổi USER_ID trong script test-email-simple.js');
    
  } catch (error) {
    console.error('❌ Error getting user info:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Message:', error.message);
    }
  }
}

// Kiểm tra argument để chọn function
const args = process.argv.slice(2);
if (args.includes('--list-users')) {
  listUsers();
} else {
  testEmailSimple();
}
